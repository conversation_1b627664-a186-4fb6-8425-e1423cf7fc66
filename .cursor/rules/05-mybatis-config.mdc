---
description: 
globs: 
alwaysApply: true
---
# MyBatis配置指南

## 配置规范
MyBatis配置已在[application.properties](mdc:src/main/resources/application.properties)中设置：
- `mybatis.mapper-locations=classpath:mappers/**/*.xml`：指定了XML映射文件的位置

## Mapper XML文件组织
每个模块的XML映射文件应遵循以下结构：
1. 在`src/main/resources/mappers`目录下为每个模块创建子目录
2. 目录名应与模块名一致，例如：
   - `src/main/resources/mappers/ragcustomermatch/`：存放客户匹配模块的映射文件

## 命名规范
- Mapper接口：`[实体名]Mapper.java`
- XML映射文件：`[实体名]Mapper.xml`
- 文件应放置在对应模块的mappers子目录中

## 示例
对于`ragcustomermatch`模块：
- Mapper接口位置：`src/main/java/com/pitaya/devdiscovery/ragcustomermatch/dao/CustomerMapper.java`
- XML映射文件位置：`src/main/resources/mappers/ragcustomermatch/CustomerMapper.xml`

## 最佳实践
- 保持Mapper接口和XML映射文件名称一致
- 在XML文件中正确设置namespace，对应到Mapper接口的完全限定名
- 使用参数注解（@Param）明确指定方法参数名
- 为复杂查询结果使用ResultMap


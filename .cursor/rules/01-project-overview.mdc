---
description: 
globs: 
alwaysApply: true
---
# Dev Discovery 项目概述

这是一个基于Spring Boot的Java项目，名为"Dev Discovery"。

## 项目结构
- 主应用入口: [DevDiscoveryApplication.java](mdc:src/main/java/com/pitaya/devdiscovery/DevDiscoveryApplication.java)
- 配置文件: [pom.xml](mdc:pom.xml)

## 技术栈
- Java 8
- Spring Boot 2.6.13
- MyBatis
- MySQL
- Lombok
- FastJSON2
- OkHttp
- HanLP (汉语言处理库)

## 模块化架构
项目采用模块化架构，每个功能模块独立开发：
- `ragcustomermatch`: 目前已有的功能模块示例
- 后续将添加更多功能模块，每个模块包含完整的MVC结构




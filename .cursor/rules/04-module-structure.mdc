---
description: 
globs: 
alwaysApply: true
---
# 模块化架构指南

## 模块化设计
本项目采用模块化设计，每个功能以独立模块方式开发，便于维护和扩展。每个模块包含自己的MVC结构。

### 现有模块
- `ragcustomermatch`: 第一个功能模块示例

### 模块内部结构
每个功能模块包含以下标准组件：
- `controller/`: REST API控制器
- `service/`: 业务逻辑实现
- `dao/`: 数据访问层（Mapper接口）
- `entity/`: 数据模型和实体类
- `dto/`: 数据传输对象
- `util/`: 模块特定工具类

## 包结构
- `com.pitaya.devdiscovery`: 主包
  - `DevDiscoveryApplication.java`: 应用程序入口
  - `[模块名]/`: 各功能模块
    - `controller/`
    - `service/`
    - `dao/`
    - `entity/`
    - 等

## XML映射文件结构
MyBatis的XML映射文件组织方式：
- 位置：`src/main/resources/mappers/`
- 每个模块在mappers下创建独立子目录：
  - `mappers/[模块名]/[实体名]Mapper.xml`
- 示例：`mappers/ragcustomermatch/CustomerMapper.xml`

## 新模块开发规范
新增功能模块时：
1. 在主包下创建新的模块包
2. 遵循标准模块内部结构
3. 在mappers目录下创建对应的子目录用于XML映射文件
4. 确保模块间低耦合高内聚
5. 公共功能抽取到共享模块

## 资源文件
资源文件位于 `src/main/resources` 目录，包括:
- 配置文件
- 静态资源
- 模板文件
- `mappers/`: MyBatis XML映射文件目录






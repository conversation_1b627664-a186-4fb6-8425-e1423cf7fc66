# 毕昇智能体调用服务

## 简介

本服务模块提供了与毕昇智能体平台的集成能力，让开发者能够方便地在Java应用中调用毕昇智能体。毕昇是一款开源LLM应用开发平台，专攻企业场景，可以通过可视化编排方式创建智能体，并通过API接口进行调用。

## 功能特点

- 支持同步调用智能体
- 支持异步调用智能体
- 支持流式输出
- 支持任务状态查询
- 自动重试和异常处理
- 支持配置化管理

## 配置说明

在`application.properties`中配置毕昇智能体相关参数：

```properties
# 毕昇智能体配置
bisheng.api.baseurl=http://example.com:30001/api/v2/workflow/invoke  # 毕昇API基础URL
bisheng.api.flowid=your-default-flow-id                              # 默认智能体流程ID
bisheng.api.timeout=60                                               # 超时时间（秒）
bisheng.api.max-retries=3                                            # 最大重试次数
bisheng.api.retry-interval=1000                                      # 重试间隔（毫秒）
bisheng.api.debug=false                                              # 是否开启调试模式
bisheng.api.async-timeout=300                                        # 异步任务超时时间（秒）
```

## 使用示例

### 1. 同步调用智能体

使用默认flowId调用智能体：

```java
@Autowired
private BishengAgentService bishengAgentService;

public void example() {
    // 准备输入参数
    Map<String, Object> inputParams = new HashMap<>();
    inputParams.put("query", "介绍一下毕昇智能体平台");
    
    // 调用智能体并获取结果
    Map<String, Object> result = bishengAgentService.invokeBishengFlow(inputParams);
    
    // 处理结果
    String answer = (String) result.get("answer");
    System.out.println("智能体回答: " + answer);
}
```

使用指定flowId调用智能体：

```java
Map<String, Object> inputParams = new HashMap<>();
inputParams.put("query", "介绍一下毕昇智能体平台");

// 使用指定的flowId
String flowId = "custom-flow-id";
Map<String, Object> result = bishengAgentService.invokeBishengFlow(flowId, inputParams);
```

### 2. 异步调用智能体

```java
Map<String, Object> inputParams = new HashMap<>();
inputParams.put("document", "这是一份需要分析的长文档...");
inputParams.put("query", "提取文档中的关键信息");

// 异步调用
CompletableFuture<Map<String, Object>> future = 
        bishengAgentService.invokeBishengFlowAsync("document-analysis-flow", inputParams);

// 添加回调处理
future.thenAccept(result -> {
    System.out.println("异步任务完成，结果: " + result);
}).exceptionally(ex -> {
    System.err.println("异步任务异常: " + ex.getMessage());
    return null;
});

// 或者等待结果
try {
    Map<String, Object> result = future.get();
    System.out.println("异步任务结果: " + result);
} catch (Exception e) {
    System.err.println("等待异步任务结果异常: " + e.getMessage());
}
```

### 3. 流式调用智能体

```java
Map<String, Object> inputParams = new HashMap<>();
inputParams.put("query", "介绍一下毕昇智能体平台");

// 流式调用，传入数据消费者
bishengAgentService.invokeBishengFlowStreaming(
        "chat-flow",
        inputParams,
        data -> {
            // 处理流式数据
            System.out.println("收到数据: " + data);
        }
);
```

### 4. 通过REST API调用

除了在应用内部直接调用服务，还可以通过暴露的REST API进行调用：

```
POST /api/bisheng/invoke
Content-Type: application/json

{
  "query": "介绍一下毕昇智能体平台"
}
```

带流程ID的调用：

```
POST /api/bisheng/invoke-with-flow
Content-Type: application/json

{
  "flowId": "custom-flow-id",
  "query": "介绍一下毕昇智能体平台"
}
```

高级调用（支持更多选项）：

```
POST /api/bisheng/invoke-advanced
Content-Type: application/json

{
  "flow_id": "custom-flow-id",
  "inputs": {
    "query": "介绍一下毕昇智能体平台"
  },
  "blocking": true,
  "stream": false,
  "timeout": 60
}
```

流式调用：

```
POST /api/bisheng/invoke-stream
Content-Type: application/json

{
  "flow_id": "chat-flow",
  "inputs": {
    "query": "介绍一下毕昇智能体平台"
  }
}
```

## 错误处理

服务会抛出`BishengAgentException`异常，包含详细的错误信息。全局异常处理器会捕获这些异常并返回友好的错误响应。

## 扩展说明

### 毕昇智能体流程ID获取方法

1. 登录毕昇平台管理页面
2. 创建或选择已有智能体流程
3. 从URL中获取流程ID，通常格式为：`https://bisheng.example.com/flow/{flowId}`

### 毕昇API格式说明

请求格式：
```json
{
  "flow_id": "your-flow-id",
  "inputs": {
    "key1": "value1",
    "key2": "value2"
  },
  "stream": false,
  "blocking": true,
  "timeout": 60
}
```

响应格式：
```json
{
  "status": "success",
  "message": "",
  "data": {
    "outputs": {
      "output1": "value1",
      "output2": "value2"
    }
  }
}
```

## 注意事项

1. 确保毕昇平台服务可访问，网络连接正常
2. 注意流程ID的正确性，错误的流程ID将导致调用失败
3. 调用智能体时，输入参数需要与毕昇平台中定义的输入节点名称一致
4. 对于大型文档处理或复杂问题，建议使用异步调用模式
5. 流式输出适合聊天或需要实时展示结果的场景 
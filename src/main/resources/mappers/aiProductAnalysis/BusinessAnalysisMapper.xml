<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.aiProductAnalysis.dao.BusinessAnalysisMapper">

    <!-- 业务分析结果映射 -->
    <resultMap id="BusinessAnalysisResultMap" type="com.pitaya.devdiscovery.aiProductAnalysis.entity.BusinessAnalysis">
        <id column="id" property="id" />
        <result column="data_type" property="dataType" />
        <result column="name" property="name" />
        <result column="content" property="content" />
        <result column="check_content_status" property="checkContentStatus" />
        <result column="keywords" property="keywords" />
        <result column="product" property="product" />
        <result column="process_status" property="processStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, data_type, name, content, check_content_status, keywords, product, process_status, create_time, update_time
    </sql>

    <!-- 查询待处理的业务分析任务列表 -->
    <select id="selectPendingBusinessAnalysis" resultMap="BusinessAnalysisResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sanquan_ai_business_analysis
        WHERE process_status = '0'
        <if test="param.dataType != null and param.dataType != ''">
            AND data_type = #{param.dataType}
        </if>
        ORDER BY create_time ASC
    </select>

    <!-- 更新业务分析结果 -->
    <update id="updateBusinessAnalysis">
        UPDATE sanquan_ai_business_analysis
        SET update_time = NOW()
        <if test="param.checkContentStatus != null and param.checkContentStatus != ''">
            , check_content_status = #{param.checkContentStatus}
        </if>
        <if test="param.keywords != null and param.keywords != ''">
            , keywords = #{param.keywords}
        </if>
        <if test="param.product != null and param.product != ''">
            , product = #{param.product}
        </if>
        <if test="param.processStatus != null and param.processStatus != ''">
            , process_status = #{param.processStatus}
        </if>
        WHERE id = #{param.id}
    </update>

    <!-- 插入业务分析记录 -->
    <insert id="insertBusinessAnalysis">
        INSERT INTO sanquan_ai_business_analysis (
            id, data_type, name, content, check_content_status, keywords, product, process_status, create_time, update_time
        ) VALUES (
            #{param.id}, #{param.dataType}, #{param.name}, #{param.content}, #{param.checkContentStatus}, 
            #{param.keywords}, #{param.product}, #{param.processStatus}, #{param.createTime}, #{param.updateTime}
        )
    </insert>

    <!-- 根据ID查询业务分析记录 -->
    <select id="selectById" resultMap="BusinessAnalysisResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sanquan_ai_business_analysis
        WHERE id = #{id}
    </select>

</mapper> 
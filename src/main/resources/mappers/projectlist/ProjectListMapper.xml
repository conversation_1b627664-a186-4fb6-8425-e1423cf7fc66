<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.projectlist.dao.ProjectListMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.projectlist.entity.ProjectList">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="list_type" property="listType" jdbcType="VARCHAR"/>
        <result column="full_project_name" property="fullProjectName" jdbcType="VARCHAR"/>
        <result column="parsed_company_name" property="parsedCompanyName" jdbcType="VARCHAR"/>
        <result column="parsed_project_name" property="parsedProjectName" jdbcType="VARCHAR"/>
        <result column="district" property="district" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础SQL字段 -->
    <sql id="Base_Column_List">
        id, list_type, full_project_name, parsed_company_name, parsed_project_name,
        district, status, create_time, update_time
    </sql>

    <!-- 根据状态和限制条数查询待处理的项目列表 -->
    <select id="selectPendingProjects" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM yantai_project_list
        WHERE status = #{status}
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 批量更新项目状态 -->
    <update id="batchUpdateStatus">
        UPDATE yantai_project_list
        SET status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据ID更新项目状态 -->
    <update id="updateStatusById">
        UPDATE yantai_project_list
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询所有项目 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM yantai_project_list
        ORDER BY create_time DESC
    </select>

</mapper> 
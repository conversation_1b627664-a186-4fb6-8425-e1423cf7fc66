<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.airagcustomermatch.dao.AiRagCustomerMatchTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.airagcustomermatch.entity.CustomerMatchTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="query" property="query" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, task_id, query, status, create_time, update_time
    </sql>

    <!-- 查询等待处理的任务ID（去重） -->
    <select id="selectWaitingTaskIds" resultType="java.lang.String">
        SELECT DISTINCT task_id
        FROM customer_match_task
        WHERE status = '0'
        ORDER BY MIN(create_time) ASC
        GROUP BY task_id
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据任务ID查询所有相关记录 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM customer_match_task
        WHERE task_id = #{taskId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据任务ID更新所有相关记录的状态 -->
    <update id="updateTaskStatusByTaskId">
        UPDATE customer_match_task
        SET status = #{status}, update_time = NOW()
        WHERE task_id = #{taskId}
    </update>

    <!-- 查询等待处理的任务记录 -->
    <select id="selectWaitingTasks" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM customer_match_task
        WHERE status = '0'
        ORDER BY create_time ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 更新单条记录的状态 -->
    <update id="updateTaskStatus">
        UPDATE customer_match_task
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 插入新任务 -->
    <insert id="insertTask" parameterType="com.pitaya.devdiscovery.airagcustomermatch.entity.CustomerMatchTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customer_match_task (task_id, query, status, create_time, update_time)
        VALUES (#{taskId}, #{query}, #{status}, NOW(), NOW())
    </insert>

    <!-- 根据主键ID查询任务 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM customer_match_task
        WHERE id = #{id}
    </select>

    <!-- 批量插入任务 -->
    <insert id="batchInsertTasks" parameterType="java.util.List">
        INSERT INTO customer_match_task (task_id, query, status, create_time, update_time)
        VALUES
        <foreach collection="tasks" item="task" separator=",">
            (#{task.taskId}, #{task.query}, #{task.status}, NOW(), NOW())
        </foreach>
    </insert>

</mapper> 
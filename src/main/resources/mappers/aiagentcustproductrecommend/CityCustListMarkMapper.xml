<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.aiagentcustproductrecommend.dao.CityCustListMarkMapper">

    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="nature_cust_id" property="natureCustId" />
        <result column="nature_cust_name" property="natureCustName" />
        <result column="base_info" property="baseInfo" />
        <result column="major_events" property="majorEvents" />
        <result column="whole_plan" property="wholePlan" />
        <result column="cooperate_opportunity" property="cooperateOpportunity" />
        <result column="marketing_opportunity" property="marketingOpportunity" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, nature_cust_id, nature_cust_name, base_info, major_events, whole_plan,
        cooperate_opportunity, marketing_opportunity, status
    </sql>

    <select id="getPendingTaskList" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM t_sanquan_city_cust_list_mark
        WHERE status = '0'
    </select>

    <select id="verifyCustMarkExist" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from t_sanquan_city_cust_list_mark
        where nature_cust_id = #{natureCustId}
            limit 1
    </select>

    <select id="getByNatureCustId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sanquan_city_cust_list_mark
        WHERE nature_cust_id = #{natureCustId} and   status = '2'
        order by update_time desc  limit 1
    </select>

    <insert id="insert" parameterType="com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_sanquan_city_cust_list_mark (nature_cust_id, nature_cust_name,industry,legal_representative,telphone,address)
        VALUES (
                   #{natureCustId},
                   #{natureCustName},
                   #{industry},
                   #{legalRepresentative},
                   #{telphone},
                   #{address}
               )
    </insert>

    <update id="updateByNatureCustId" parameterType="com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark">
        UPDATE t_sanquan_city_cust_list_mark
        <set>
            <if test="industry != null and industry != ''">
                industry = #{industry},
            </if>
            <if test="legalRepresentative != null and legalRepresentative != ''">
                legal_representative = #{legalRepresentative},
            </if>
            <if test="telphone != null and telphone != ''">
                telphone = #{telphone},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="baseInfo != null">
                base_info = #{baseInfo},
            </if>
            <if test="majorEvents != null">
                major_events = #{majorEvents},
            </if>
            <if test="wholePlan != null">
                whole_plan = #{wholePlan},
            </if>
            <if test="cooperateOpportunity != null">
                cooperate_opportunity = #{cooperateOpportunity},
            </if>
            <if test="summary != null">
                summary = #{summary},
            </if>
            <if test="keywords != null">
                keywords = #{keywords},
            </if>
            <if test="marketingOpportunity != null">
                marketing_opportunity = #{marketingOpportunity},
            </if>
            <if test="result != null">
                result = #{result},
            </if>
            <if test="processStatus != null">
                process_status = #{processStatus},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        WHERE nature_cust_id = #{natureCustId}
    </update>

    <update id="updateStatusByNatureCustId">
        UPDATE t_sanquan_city_cust_list_mark
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="processStatus != null">
                process_status = #{processStatus},
            </if>
            <if test="remark != null">
                remark = #{remark}
            </if>
        </set>
        WHERE nature_cust_id = #{natureCustId}
    </update>

</mapper>

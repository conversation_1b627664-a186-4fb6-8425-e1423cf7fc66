<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.aivisitquality.dao.VisitQualityAnalysisMapper">
    <resultMap id="VisitQualityAnalysisResultMap" type="com.pitaya.devdiscovery.aivisitquality.entity.VisitQualityAnalysis">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="custom_name" property="customName" />
        <result column="visit_content" property="visitContent" />
        <result column="status" property="status" />
        <result column="total_score" property="totalScore" />
        <result column="final_summary" property="finalSummary" />
        <result column="score_details" property="scoreDetails" />
        <result column="business_score" property="businessScore" />
        <result column="business_reason" property="businessReason" />
        <result column="process_score" property="processScore" />
        <result column="process_reason" property="processReason" />
        <result column="level_score" property="levelScore" />
        <result column="level_reason" property="levelReason" />
        <result column="content_score" property="contentScore" />
        <result column="content_reason" property="contentReason" />
        <result column="deduct_score" property="deductScore" />
        <result column="deduct_reason" property="deductReason" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, record_id, custom_name, visit_content, status, total_score, final_summary, score_details,
        business_score, business_reason, process_score, process_reason, level_score, level_reason,
        content_score, content_reason, deduct_score, deduct_reason, create_time, update_time
    </sql>

    <select id="selectPendingVisitQualityAnalysis" resultMap="VisitQualityAnalysisResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sanquan_ai_visit_quality_analysis
        WHERE status = '0'
        <if test="param.recordId != null and param.recordId != ''">
            AND record_id = #{param.recordId}
        </if>
        ORDER BY create_time ASC
    </select>

    <update id="updateVisitQualityAnalysis">
        UPDATE t_sanquan_ai_visit_quality_analysis
        SET update_time = NOW()
        <if test="param.status != null and param.status != ''">
            , status = #{param.status}
        </if>
        <if test="param.totalScore != null and param.totalScore != ''">
            , total_score = #{param.totalScore}
        </if>
        <if test="param.finalSummary != null and param.finalSummary != ''">
            , final_summary = #{param.finalSummary}
        </if>
        <if test="param.scoreDetails != null and param.scoreDetails != ''">
            , score_details = #{param.scoreDetails}
        </if>
        <if test="param.businessScore != null and param.businessScore != ''">
            , business_score = #{param.businessScore}
        </if>
        <if test="param.businessReason != null and param.businessReason != ''">
            , business_reason = #{param.businessReason}
        </if>
        <if test="param.processScore != null and param.processScore != ''">
            , process_score = #{param.processScore}
        </if>
        <if test="param.processReason != null and param.processReason != ''">
            , process_reason = #{param.processReason}
        </if>
        <if test="param.levelScore != null and param.levelScore != ''">
            , level_score = #{param.levelScore}
        </if>
        <if test="param.levelReason != null and param.levelReason != ''">
            , level_reason = #{param.levelReason}
        </if>
        <if test="param.contentScore != null and param.contentScore != ''">
            , content_score = #{param.contentScore}
        </if>
        <if test="param.contentReason != null and param.contentReason != ''">
            , content_reason = #{param.contentReason}
        </if>
        <if test="param.deductScore != null and param.deductScore != ''">
            , deduct_score = #{param.deductScore}
        </if>
        <if test="param.deductReason != null and param.deductReason != ''">
            , deduct_reason = #{param.deductReason}
        </if>
        WHERE id = #{param.id}
    </update>

    <!-- 插入拜访质量分析记录 -->
    <insert id="insertVisitQualityAnalysis">
        INSERT INTO t_sanquan_ai_visit_quality_analysis (
            record_id, custom_name, visit_content, status, total_score, final_summary, score_details,
            business_score, business_reason, process_score, process_reason, level_score, level_reason,
            content_score, content_reason, deduct_score, deduct_reason, create_time, update_time
        ) VALUES (
            #{param.recordId}, #{param.customName}, #{param.visitContent}, #{param.status}, #{param.totalScore}, 
            #{param.finalSummary}, #{param.scoreDetails}, #{param.businessScore}, #{param.businessReason}, 
            #{param.processScore}, #{param.processReason}, #{param.levelScore}, #{param.levelReason},
            #{param.contentScore}, #{param.contentReason}, #{param.deductScore}, #{param.deductReason}, 
            #{param.createTime}, #{param.updateTime}
        )
    </insert>

    <!-- 根据ID查询拜访质量分析记录 -->
    <select id="selectById" resultMap="VisitQualityAnalysisResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sanquan_ai_visit_quality_analysis
        WHERE id = #{id}
    </select>

    <!-- 根据记录ID查询拜访质量分析记录 -->
    <select id="selectByRecordId" resultMap="VisitQualityAnalysisResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sanquan_ai_visit_quality_analysis
        WHERE record_id = #{recordId}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.aiability.dao.CustomerMatchLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.aiability.entity.CustomerMatchLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="query" property="query" jdbcType="VARCHAR"/>
        <result column="answer" property="answer" jdbcType="VARCHAR"/>
        <result column="intent" property="intent" jdbcType="VARCHAR"/>
        <result column="question" property="question" jdbcType="VARCHAR"/>
        <result column="reserve1" property="reserve1" jdbcType="VARCHAR"/>
        <result column="reserve2" property="reserve2" jdbcType="VARCHAR"/>
        <result column="reserve3" property="reserve3" jdbcType="VARCHAR"/>
        <result column="reserve4" property="reserve4" jdbcType="VARCHAR"/>
        <result column="reserve5" property="reserve5" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="VARCHAR"/>
        <result column="result_count" property="resultCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, request_id, query, answer, intent, question, reserve1, reserve2, reserve3, 
        reserve4, reserve5, score, result_count, create_time, update_time
    </sql>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO customer_match_log (
            request_id, query, answer, intent, question, 
            reserve1, reserve2, reserve3, reserve4, reserve5, 
            score, result_count
        ) VALUES
        <foreach collection="logs" item="log" separator=",">
            (
                #{log.requestId}, #{log.query}, #{log.answer}, #{log.intent}, #{log.question},
                #{log.reserve1}, #{log.reserve2}, #{log.reserve3}, #{log.reserve4}, #{log.reserve5},
                #{log.score}, #{log.resultCount}
            )
        </foreach>
    </insert>

    <!-- 根据请求ID查询 -->
    <select id="selectByRequestId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM customer_match_log
        WHERE request_id = #{requestId}
        ORDER BY score DESC
    </select>

    <!-- 根据查询内容查询历史记录 -->
    <select id="selectByQuery" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM customer_match_log
        WHERE query = #{query}
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

</mapper> 
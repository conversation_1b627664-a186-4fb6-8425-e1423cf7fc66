<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.ragcustomermatch.dao.CustomerMatchTaskMapper">
    
    <!-- 定义CustomerMatchTask的结果映射 -->
    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 查询未处理的任务列表 -->
    <select id="selectUnprocessedTasks" resultMap="BaseResultMap">
        SELECT id, status
        FROM t_sanquan_rag_customer_match_task
        WHERE status = '0'
    </select>
    
    <!-- 更新任务状态 -->
    <update id="updateTaskStatus">
        UPDATE t_sanquan_rag_customer_match_task
        SET status = #{status}
        WHERE id = #{id}
    </update>
    
</mapper> 
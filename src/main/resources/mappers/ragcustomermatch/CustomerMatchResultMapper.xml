<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.ragcustomermatch.dao.CustomerMatchResultMapper">
    
    <!-- 定义CustomerMatchResult的结果映射 -->
    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchResult">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="eparchy_name" property="eparchyName" jdbcType="VARCHAR"/>
        <result column="customer_name_query" property="customerNameQuery" jdbcType="VARCHAR"/>
        <result column="customer_eparchy_name" property="customerEparchyName" jdbcType="VARCHAR"/>
        <result column="customer_id" property="customerId" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 根据任务ID查询处理结果 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT id, task_id, eparchy_name, customer_name_query, customer_eparchy_name, customer_id, customer_name
        FROM t_sanquan_rag_customer_match_result
        WHERE task_id = #{taskId}
    </select>
    
    <!-- 批量插入处理结果 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_sanquan_rag_customer_match_result
        (task_id, eparchy_name, customer_name_query, customer_eparchy_name, customer_id, customer_name)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.taskId},
            #{item.eparchyName},
            #{item.customerNameQuery},
            #{item.customerEparchyName},
            #{item.customerId},
            #{item.customerName}
            )
        </foreach>
    </insert>
    
</mapper> 
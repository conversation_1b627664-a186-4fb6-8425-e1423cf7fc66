<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.ragcustomermatch.dao.CustomerMatchAimMapper">
    
    <!-- 定义CustomerMatchAim的结果映射 -->
    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchAim">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="eparchy_name" property="eparchyName" jdbcType="VARCHAR"/>
        <result column="customer_name_query" property="customerNameQuery" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 根据任务ID查询待处理数据 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT id, task_id, eparchy_name, customer_name_query
        FROM t_sanquan_rag_customer_match_aim
        WHERE task_id = #{taskId}
    </select>
    
</mapper> 
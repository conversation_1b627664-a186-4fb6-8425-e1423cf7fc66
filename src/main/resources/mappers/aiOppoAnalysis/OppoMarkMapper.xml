<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.aiOppoAnalysis.mapper.OppoMarkMapper">
    <resultMap id="OppoMarkPoolsResultMap" type="com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoMarkPools">
        <result column="oppo_number" property="oppoNumber" />
        <result column="oppo_name" property="oppoName" />
        <result column="khxqjj" property="khxqjj" />
    </resultMap>
    <select id="selectPendingOppoMark" resultMap="OppoMarkPoolsResultMap">
        SELECT t0.oppo_number,
               t1.oppo_name,
               t1.khxqjj
        FROM t_sanquan_zq_oppo_mark t0, t_sanquan_zq_oppo_pools t1
        WHERE t0.oppo_number = t1.oppo_number
        AND t0.mark_status = '0'
        <if test="param.dayId != null and param.dayId != ''">
            AND t0.day_id = #{param.dayId}
        </if>
        ORDER BY t0.update_time DESC
    </select>
    <update id="updateOppoMarkStatus">
        UPDATE t_sanquan_zq_oppo_mark
        SET mark_status = '1',
            update_time = NOW()
            <if test="param.productAi != null and param.productAi != ''">
                , product_ai = #{param.productAi}
            </if>
            <if test="param.reqKeyword != null and param.reqKeyword != ''">
                , req_keyword = #{param.reqKeyword}
            </if>
            <if test="param.markType != null and param.markType != ''">
                , mark_type = #{param.markType}
            </if>
            <if test="param.firstAuditPass != null and param.firstAuditPass != ''">
                , first_audit_pass = #{param.firstAuditPass}
            </if>
        WHERE oppo_number = #{param.oppoNumber}
    </update>

</mapper>

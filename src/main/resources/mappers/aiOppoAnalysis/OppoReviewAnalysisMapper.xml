<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.aiOppoAnalysis.mapper.OppoReviewAnalysisMapper">
    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoReviewAnalysis">
        <id column="id" property="id" />
        <result column="oppo_number" property="oppoNumber" />
        <result column="contract_amount_prev" property="contractAmountPrev" />
        <result column="contract_amount" property="contractAmount" />
        <result column="oppo_ls_info" property="oppoLsInfo" />
        <result column="sfyx_oppo" property="sfyxOppo" />
        <result column="sfjx_track" property="sfjxTrack" />
        <result column="sjgjlx_m" property="sjgjlxM" />
        <result column="sjgjjy_m" property="sjgjjyM" />
        <result column="create_by_m" property="createByM" />
        <result column="create_time_m" property="createTimeM" />
        <result column="create_name_m" property="createNameM" />
        <result column="status_m" property="statusM" />
        <result column="back_remark" property="backRemark" />
        <result column="rzzjfjh" property="rzzjfjh" />
        <result column="rlty" property="rlty" />
        <result column="rsafe" property="rsafe" />
        <result column="sjgjlx_jc" property="sjgjlxJc" />
        <result column="sjgjjy_jc" property="sjgjjyJc" />
        <result column="create_by_jc" property="createByJc" />
        <result column="create_time_jc" property="createTimeJc" />
        <result column="create_name_jc" property="createNameJc" />
        <result column="status_jc" property="statusJc" />
        <result column="rbpjh" property="rbpjh" />
        <result column="ydwabp" property="ydwabp" />
        <result column="rzx" property="rzx" />
        <result column="rgzsj" property="rgzsj" />
        <result column="r5gzw" property="r5gzw" />
        <result column="sfkjzyjlcpkjjcys" property="sfkjzyjlcpkjjcys" />
        <result column="sjgjlx_yy" property="sjgjlxYy" />
        <result column="sjgjjy_yy" property="sjgjjyYy" />
        <result column="create_by_yy" property="createByYy" />
        <result column="create_time_yy" property="createTimeYy" />
        <result column="create_name_yy" property="createNameYy" />
        <result column="status_yy" property="statusYy" />
        <result column="rzycpjh" property="rzycpjh" />
        <result column="spmlzycp" property="spmlzycp" />
        <result column="spfmlzycp" property="spfmlzycp" />
        <result column="rwlw" property="rwlw" />
        <result column="rdsj" property="rdsj" />
        <result column="rai" property="rai" />
        <result column="sjgjlx_cy" property="sjgjlxCy" />
        <result column="sjgjjy_cy" property="sjgjjyCy" />
        <result column="cpjh" property="cpjh" />
        <result column="cpgjjy" property="cpgjjy" />
        <result column="create_by_cy" property="createByCy" />
        <result column="create_time_cy" property="createTimeCy" />
        <result column="create_name_cy" property="createNameCy" />
        <result column="status_cy" property="statusCy" />
        <result column="sjgjlx_result" property="sjgjlxResult" />
        <result column="sjgjjy_result" property="sjgjjyResult" />
        <result column="oppo_status" property="oppoStatus" />
        <result column="day_id" property="dayId" />
        <result column="version" property="version" />
        <result column="is_update" property="isUpdate" />
        <result column="is_handle_cpyf" property="isHandleCpyf" />
        <result column="is_handle_yyfw" property="isHandleYyfw" />
        <result column="is_handle_jcjf" property="isHandleJcjf" />
    </resultMap>
    <sql id="Base_Column_List">
        id, oppo_number, contract_amount_prev, contract_amount, oppo_ls_info, sfyx_oppo, sfjx_track,
        sjgjlx_m, sjgjjy_m, create_by_m, create_time_m, create_name_m, status_m, back_remark,
        rzzjfjh, rlty, rsafe, sjgjlx_jc, sjgjjy_jc, create_by_jc, create_time_jc, create_name_jc, status_jc,
        rbpjh, ydwabp, rzx, rgzsj, r5gzw, sfkjzyjlcpkjjcys, sjgjlx_yy, sjgjjy_yy, create_by_yy,
        create_time_yy, create_name_yy, status_yy, rzycpjh, spmlzycp, spfmlzycp, rwlw, rdsj, rai,
        sjgjlx_cy, sjgjjy_cy, cpjh, cpgjjy, create_by_cy, create_time_cy, create_name_cy, status_cy,
        sjgjlx_result, sjgjjy_result, oppo_status, day_id, version, is_update, is_handle_cpyf,
        is_handle_yyfw, is_handle_jcjf
    </sql>
    <select id="selectPendingOppoReview" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sanquan_oppo_review
        <where>
            sjgjjy_result is null and status_jc = '1' and status_yy = '1' and status_cy = '1'
            <if test="param.dayId != null and param.dayId != ''">
                AND day_id = #{param.dayId}
            </if>
        </where>
        ORDER BY create_time_m DESC
    </select>
    <update id="updateOppoReview">
        UPDATE t_sanquan_oppo_review
        SET sjgjjy_result = #{param.sjgjjyResult},oppo_status = '1'
        WHERE id = #{param.id}
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.dstagbiaoxun.dao.TsanquanDsBiaoxuncheckMapper">

    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.dstagbiaoxun.entity.TsanquanDsBiaoxuncheck">
        <result column="id" property="id"/>
        <result column="natural_customer_id" property="naturalCustomerId"/>
        <result column="natural_customer_name" property="naturalCustomerName"/>
        <result column="bidding_title" property="biddingTitle"/>
        <result column="bidding_content" property="biddingContent"/>
        <result column="release_time" property="releaseTime"/>
        <result column="bid_winner" property="bidWinner"/>
        <result column="quantity" property="quantity"/>
        <result column="city" property="city"/>
        <result column="project_number" property="projectNumber"/>
        <result column="project_name" property="projectName"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 根据natural_customer_id查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM t_sanquan_ds_biaoxuncheck
        WHERE natural_customer_id = #{naturalCustomerId}
    </select>

    <!-- 根据status查询列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT * FROM t_sanquan_ds_biaoxuncheck
        WHERE status = #{status}
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_sanquan_ds_biaoxuncheck (
            natural_customer_id, natural_customer_name, bidding_title,
            bidding_content, release_time, bid_winner, quantity,
            city, project_number, project_name, status
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.naturalCustomerId}, #{item.naturalCustomerName}, #{item.biddingTitle},
            #{item.biddingContent}, #{item.releaseTime}, #{item.bidWinner}, #{item.quantity},
            #{item.city}, #{item.projectNumber}, #{item.projectName}, #{item.status}
            )
        </foreach>
    </insert>

    <!-- 批量更新status -->
    <update id="batchUpdateStatus">
        UPDATE t_sanquan_ds_biaoxuncheck
        SET status = #{status}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据id更新status -->
    <update id="updateStatusById">
        UPDATE t_sanquan_ds_biaoxuncheck
        SET status = #{status}
        WHERE natural_customer_id = #{naturalCustomerId}
    </update>

</mapper>

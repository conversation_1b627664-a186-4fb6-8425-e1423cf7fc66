<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.dsbusinessopportunity.dao.DsOpportunityAnalyseMapper">

    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsOpportunityAnalyse">
        <id column="id" property="id" />
        <result column="opportunity_id" property="opportunityId" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="opportunity_title" property="opportunityTitle" />
        <result column="opportunity_content" property="opportunityContent" />
        <result column="opportunity_unit" property="opportunityUnit" />
        <result column="opportunity_amount" property="opportunityAmount" />
        <result column="keywords" property="keywords" />
        <result column="product" property="product" />
        <result column="digital_flag" property="digitalFlag" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, opportunity_id, customer_id, customer_name, opportunity_title, opportunity_content, opportunity_unit, opportunity_amount,
        keywords, product, digital_flag, status, create_time, update_time
    </sql>

    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_ds_opportunity_analyse
        where id = #{id}
    </select>

    <select id="findByStatus" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_ds_opportunity_analyse
        where status = #{status}
    </select>

    <update id="updateMatchInfo" parameterType="com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsOpportunityAnalyse">
        update t_sanquan_ds_opportunity_analyse
        <set>
            <if test="keywords != null">
                keywords = #{keywords},
            </if>
            <if test="product != null">
                product = #{product},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="digitalFlag != null">
                digital_flag = #{digitalFlag},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="batchUpdateDigitalFlagByIds">
        update t_sanquan_ds_opportunity_analyse
        set digital_flag = #{digitalFlag}
        where id IN
        <foreach item="item_id" collection="ids" open="(" separator="," close=")">
            #{item_id}
        </foreach>
    </update>

    <update id="batchUpdateStatusByIds">
        update t_sanquan_ds_opportunity_analyse
        set status = #{status}
        where id IN
        <foreach item="item_id" collection="ids" open="(" separator="," close=")">
            #{item_id}
        </foreach>
    </update>
</mapper>

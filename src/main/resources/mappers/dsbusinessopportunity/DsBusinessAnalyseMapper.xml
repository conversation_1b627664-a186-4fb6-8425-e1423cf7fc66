<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pitaya.devdiscovery.dsbusinessopportunity.dao.DsBusinessAnalyseMapper">

    <resultMap id="BaseResultMap" type="com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsBusinessAnalyse">
        <id column="id" property="id" />
        <result column="business_id" property="businessId" />
        <result column="business_name" property="businessName" />
        <result column="business_content" property="businessContent" />
        <result column="business_amount" property="businessAmount" />
        <result column="keywords" property="keywords" />
        <result column="product" property="product" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, business_id, business_name, business_content, business_amount, keywords, product,
        status, create_time, update_time
    </sql>

    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_ds_business_analyse
        where id = #{id}
    </select>

    <select id="findByStatus" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_ds_business_analyse
        where status = #{status}
    </select>

    <update id="updateMatchInfo" parameterType="com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsBusinessAnalyse">
        update t_sanquan_ds_business_analyse
        <set>
            <if test="keywords != null">
                keywords = #{keywords},
            </if>
            <if test="product != null">
                product = #{product},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            update_time = NOW()
        </set>
        where id = #{id}
    </update>

</mapper> 
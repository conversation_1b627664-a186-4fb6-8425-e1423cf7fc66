-- 业务分析表状态迁移脚本
-- 新增"正在处理"状态，调整现有状态值

-- 1. 备份现有数据（可选）
-- CREATE TABLE sanquan_ai_business_analysis_backup AS SELECT * FROM sanquan_ai_business_analysis;

-- 2. 更新现有数据的状态值
-- 将原来的状态值调整为新的状态值
UPDATE sanquan_ai_business_analysis 
SET process_status = CASE 
    WHEN process_status = '1' THEN '2'  -- 原"已处理"改为"已处理"
    WHEN process_status = '2' THEN '3'  -- 原"处理失败"改为"处理失败"
    ELSE process_status                 -- "待处理"保持不变
END
WHERE process_status IN ('1', '2');

-- 3. 更新表注释
ALTER TABLE sanquan_ai_business_analysis 
MODIFY COLUMN process_status char(1) NOT NULL DEFAULT '0' 
COMMENT '处理状态：0-待处理，1-正在处理，2-已处理，3-处理失败';

-- 4. 验证数据迁移结果
SELECT 
    process_status,
    CASE 
        WHEN process_status = '0' THEN '待处理'
        WHEN process_status = '1' THEN '正在处理'
        WHEN process_status = '2' THEN '已处理'
        WHEN process_status = '3' THEN '处理失败'
        ELSE '未知状态'
    END AS status_desc,
    COUNT(*) as count
FROM sanquan_ai_business_analysis 
GROUP BY process_status 
ORDER BY process_status;

-- 5. 检查是否有异常状态
SELECT * FROM sanquan_ai_business_analysis 
WHERE process_status NOT IN ('0', '1', '2', '3');

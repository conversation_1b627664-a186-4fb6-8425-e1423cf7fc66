-- 客户匹配请求日志表
-- 用于存储每次客户匹配请求的查询内容和返回结果
CREATE TABLE `customer_match_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(64) NOT NULL COMMENT '请求ID，用于标识同一次请求的多条结果',
  `query` varchar(500) NOT NULL COMMENT '查询内容（如公司名称）',
  `answer` varchar(500) DEFAULT NULL COMMENT '答案',
  `intent` varchar(200) DEFAULT NULL COMMENT '意图',
  `question` varchar(500) DEFAULT NULL COMMENT '问题/匹配到的公司名称',
  `reserve1` varchar(100) DEFAULT NULL COMMENT '预留字段1-权限标识',
  `reserve2` varchar(100) DEFAULT NULL COMMENT '预留字段2-地区',
  `reserve3` varchar(100) DEFAULT NULL COMMENT '预留字段3-企业ID/编码',
  `reserve4` varchar(500) DEFAULT NULL COMMENT '预留字段4',
  `reserve5` varchar(500) DEFAULT NULL COMMENT '预留字段5',
  `score` varchar(50) DEFAULT NULL COMMENT '匹配分数',
  `result_count` int(11) DEFAULT NULL COMMENT '本次请求返回的总结果数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`) COMMENT '请求ID索引',
  KEY `idx_query` (`query`) COMMENT '查询内容索引', 
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引',
  KEY `idx_reserve2` (`reserve2`) COMMENT '地区索引',
  KEY `idx_reserve3` (`reserve3`) COMMENT '企业ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户匹配请求日志表';

-- 索引说明：
-- idx_request_id: 用于查询同一次请求的所有结果
-- idx_query: 用于按查询内容检索历史记录
-- idx_create_time: 用于按时间范围查询
-- idx_reserve2: 用于按地区统计分析
-- idx_reserve3: 用于按企业ID查询 
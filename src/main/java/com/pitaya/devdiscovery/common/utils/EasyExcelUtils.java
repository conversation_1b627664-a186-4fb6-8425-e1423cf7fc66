package com.pitaya.devdiscovery.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * EasyExcel工具类
 */
@Slf4j
public class EasyExcelUtils {

    /**
     * 读取Excel文件
     *
     * @param filePath Excel文件路径
     * @param clazz    数据对象类型
     * @param consumer 数据处理函数
     * @param <T>      数据对象类型
     */
    public static <T> void readExcel(String filePath, Class<T> clazz, Consumer<List<T>> consumer) {
        readExcel(filePath, clazz, consumer, 500);
    }

    /**
     * 读取Excel文件
     *
     * @param filePath  Excel文件路径
     * @param clazz     数据对象类型
     * @param consumer  数据处理函数
     * @param batchSize 批处理大小
     * @param <T>       数据对象类型
     */
    public static <T> void readExcel(String filePath, Class<T> clazz, Consumer<List<T>> consumer, int batchSize) {
        File file = new File(filePath);
        if (!file.exists()) {
            log.error("文件不存在: {}", filePath);
            return;
        }

        EasyExcel.read(filePath, clazz, new BatchDataListener<>(consumer, batchSize)).sheet().doRead();
    }

    /**
     * 批量数据监听器
     *
     * @param <T> 数据对象类型
     */
    private static class BatchDataListener<T> extends AnalysisEventListener<T> {
        private final List<T> dataList = new ArrayList<>();
        private final Consumer<List<T>> consumer;
        private final int batchSize;

        public BatchDataListener(Consumer<List<T>> consumer, int batchSize) {
            this.consumer = consumer;
            this.batchSize = batchSize;
        }

        @Override
        public void invoke(T data, AnalysisContext context) {
            dataList.add(data);
            if (dataList.size() >= batchSize) {
                consumer.accept(new ArrayList<>(dataList));
                dataList.clear();
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            if (!dataList.isEmpty()) {
                consumer.accept(new ArrayList<>(dataList));
                dataList.clear();
            }
        }
    }
}

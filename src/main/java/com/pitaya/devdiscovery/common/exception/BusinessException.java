package com.pitaya.devdiscovery.common.exception;

/**
 * 业务异常类
 */
public class BusinessException extends RuntimeException {
    /**
     * 错误代码
     */
    private String code;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        this("500", message);
    }

    /**
     * 构造函数
     *
     * @param code    错误代码
     * @param message 错误消息
     */
    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getCode() {
        return code;
    }
}
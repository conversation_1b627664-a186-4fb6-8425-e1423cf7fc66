package com.pitaya.devdiscovery.common.response;

import lombok.Data;

/**
 * 通用响应类
 *
 * @param <T> 响应数据类型
 */
@Data
public class BaseResponse<T> {
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应代码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 创建成功响应
     *
     * @param <T> 数据类型
     * @return 成功响应对象
     */
    public static <T> BaseResponse<T> success() {
        return success(null);
    }

    /**
     * 创建成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 成功响应对象
     */
    public static <T> BaseResponse<T> success(T data) {
        return success("200", "操作成功", data);
    }

    /**
     * 创建成功响应
     *
     * @param code    响应代码
     * @param message 响应消息
     * @param data    响应数据
     * @param <T>     数据类型
     * @return 成功响应对象
     */
    public static <T> BaseResponse<T> success(String code, String message, T data) {
        BaseResponse<T> response = new BaseResponse<>();
        response.setSuccess(true);
        response.setCode(code);
        response.setMessage(message);
        response.setData(data);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param <T> 数据类型
     * @return 失败响应对象
     */
    public static <T> BaseResponse<T> error() {
        return error("500", "操作失败");
    }

    /**
     * 创建失败响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 失败响应对象
     */
    public static <T> BaseResponse<T> error(String message) {
        return error("500", message);
    }

    /**
     * 创建失败响应
     *
     * @param code    错误代码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 失败响应对象
     */
    public static <T> BaseResponse<T> error(String code, String message) {
        BaseResponse<T> response = new BaseResponse<>();
        response.setSuccess(false);
        response.setCode(code);
        response.setMessage(message);
        return response;
    }
}
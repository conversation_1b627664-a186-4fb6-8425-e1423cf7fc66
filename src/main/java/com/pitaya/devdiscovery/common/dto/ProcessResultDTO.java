package com.pitaya.devdiscovery.common.dto;

import lombok.Data;

/**
 * 流程处理结果数据传输对象
 */
@Data
public class ProcessResultDTO {
    /**
     * 处理数量
     */
    private int processedCount;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 创建处理结果DTO
     *
     * @param processedCount 处理数量
     * @param message        提示信息
     * @return 处理结果DTO
     */
    public static ProcessResultDTO of(int processedCount, String message) {
        ProcessResultDTO dto = new ProcessResultDTO();
        dto.setProcessedCount(processedCount);
        dto.setMessage(message);
        return dto;
    }
}
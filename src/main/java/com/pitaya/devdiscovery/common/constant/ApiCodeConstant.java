package com.pitaya.devdiscovery.common.constant;

/**
 * API响应代码常量类
 */
public class ApiCodeConstant {
    /**
     * 成功
     */
    public static final String SUCCESS = "200";

    /**
     * 系统错误
     */
    public static final String SYSTEM_ERROR = "500";

    /**
     * 参数错误
     */
    public static final String PARAM_ERROR = "400";

    /**
     * 未授权
     */
    public static final String UNAUTHORIZED = "401";

    /**
     * 禁止访问
     */
    public static final String FORBIDDEN = "403";

    /**
     * 资源不存在
     */
    public static final String NOT_FOUND = "404";

    /**
     * 业务错误
     */
    public static final String BUSINESS_ERROR = "600";

    private ApiCodeConstant() {
        // 私有构造函数，防止实例化
    }
}
package com.pitaya.devdiscovery.aiability.dto.ragcustomermatch;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.Arrays;
import java.util.List;


@Data
public class IntentRecognitionRequest {

    /**
     * 查询内容
     */
    private String query;

    /**
     * 权限数组
     */
    private List<String> power;

    /**
     * 索引名称
     */
    private String index;

    /**
     * 分数阈值
     */
    private Double score;

    /**
     * 输出格式
     */
    private String output;

    /**
     * 搜索top数量
     */
    @JSONField(name = "search top")
    private Integer searchTop;

    /**
     * top k数量
     */
    @JSONField(name = "top k")
    private Integer topK;

    /**
     * 构造方法 - 创建默认的客户匹配请求
     *
     * @param query 查询内容
     * @return IntentRecognitionRequest
     */
    public static IntentRecognitionRequest createCustomerMatchRequest(String query) {
        IntentRecognitionRequest request = new IntentRecognitionRequest();
        request.setQuery(query);
        request.setPower(Arrays.asList("zq"));
        request.setIndex("zq_customer");
        request.setScore(0.02);
        request.setOutput("");
        request.setSearchTop(50);
        request.setTopK(10);
        return request;
    }
}

package com.pitaya.devdiscovery.aiability.dto;

import lombok.Data;

/**
 * @program: dev-discovery
 * @ClassName BatchQueryRequest
 * @description: 批量查询请求DTO
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Data
public class BatchQueryRequest {

    /**
     * 逗号分隔的查询内容，例如："自然资源,国土,物流"
     */
    private String queries;

    /**
     * 是否返回详细数据（可选，默认false只返回统计信息）
     */
    private Boolean includeDetails = false;
}
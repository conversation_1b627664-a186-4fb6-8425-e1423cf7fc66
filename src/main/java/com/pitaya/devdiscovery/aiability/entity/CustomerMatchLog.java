package com.pitaya.devdiscovery.aiability.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: dev-discovery
 * @ClassName CustomerMatchLog
 * @description: 客户匹配请求日志实体类
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Data
public class CustomerMatchLog {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 请求ID，用于标识同一次请求的多条结果
     */
    private String requestId;

    /**
     * 查询内容（如公司名称）
     */
    private String query;

    /**
     * 答案
     */
    private String answer;

    /**
     * 意图
     */
    private String intent;

    /**
     * 问题/匹配到的公司名称
     */
    private String question;

    /**
     * 预留字段1-权限标识
     */
    private String reserve1;

    /**
     * 预留字段2-地区
     */
    private String reserve2;

    /**
     * 预留字段3-企业ID/编码
     */
    private String reserve3;

    /**
     * 预留字段4
     */
    private String reserve4;

    /**
     * 预留字段5
     */
    private String reserve5;

    /**
     * 匹配分数
     */
    private String score;

    /**
     * 本次请求返回的总结果数
     */
    private Integer resultCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
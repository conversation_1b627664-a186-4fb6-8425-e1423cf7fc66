/*
 * Copyright (c) 2022, <EMAIL>. All rights reserved.
 */

package com.pitaya.devdiscovery.aiability.utils;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.crypto.digest.mac.Mac;

import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;


public class HMacAuthUtil {
	/**
	 * 空字符分隔
	 */
	public final static String BLANK_SEPARATOR = " ";

	/**
	 * 双引号
	 */
	public final static String FIELD_DESCRIPTOR = "\"";

	/**
	 * 逗号空格分隔
	 */
	public final static String HEADER_SEGMENT_SEPARATOR = ", ";

	/**
	 * 算法映射
	 */
	public final static Map<String, HmacAlgorithm> ALGORITHM_MAPPING = new HashMap<>(2);

	/**
	 * 算法范围
	 */
	public final static List<String> ALGORITHM_RANGE = Arrays.asList("hmac-sha256", "hmac-sha1");

	static {
		ALGORITHM_MAPPING.put("hmac-sha256", HmacAlgorithm.HmacSHA256);
		ALGORITHM_MAPPING.put("hmac-sha1", HmacAlgorithm.HmacSHA1);
	}


	/**
	 * 构建头信息体
	 *
	 * @param url       url
	 * @param algorithm 算法
	 * @param username  用户
	 * @param date      日期 GMT格式
	 * @param secret    密钥
	 * @param method    方法
	 * @param path      路径
	 * @param protocol  协议
	 * @return
	 */
	public static String buildAuthorization(UrlBuilder url, String algorithm, String username, String date, String secret, String method, String path, String protocol) {
		StringBuilder stb = new StringBuilder();
		return stb.append("hmac").append(BLANK_SEPARATOR)
				.append("username=").append(FIELD_DESCRIPTOR).append(username).append(FIELD_DESCRIPTOR).append(HEADER_SEGMENT_SEPARATOR)
				.append("algorithm=").append(FIELD_DESCRIPTOR).append(algorithm).append(FIELD_DESCRIPTOR).append(HEADER_SEGMENT_SEPARATOR)
				.append("headers=").append(FIELD_DESCRIPTOR)
				.append("date").append(BLANK_SEPARATOR)
				.append("request-line").append(BLANK_SEPARATOR)
				.append("host")
				.append(FIELD_DESCRIPTOR).append(HEADER_SEGMENT_SEPARATOR)
				.append("signature=").append(FIELD_DESCRIPTOR).append(signature(url, algorithm, date, secret, method, path, protocol)).append(FIELD_DESCRIPTOR).toString();

	}

	/**
	 * 构建签名
	 *
	 * @param url       url
	 * @param algorithm 算法
	 * @param date      日期
	 * @param secret    密钥
	 * @param method    方法
	 * @param path      路径
	 * @param protocol  协议
	 * @return
	 */
	public static String signature(UrlBuilder url, String algorithm, String date, String secret, String method, String path, String protocol) {
		StringBuilder stb = new StringBuilder();

		String digestContent = stb.append("date: ")
				.append(date)
				.append("\n")
				.append(method)
				.append(BLANK_SEPARATOR)
				.append(path)
				.append(BLANK_SEPARATOR)
				.append(protocol)
				.append("\n")
				.append("host: ")
				.append(url.getAuthority()).toString();
		System.out.println(digestContent);
		Mac mac = new HMac(ALGORITHM_MAPPING.get(algorithm), secret.getBytes(StandardCharsets.UTF_8));
		return mac.digestBase64(digestContent, false);
	}

	/**
	 * 获取GMT格式的日期 当前日期
	 *
	 * @return
	 */
	public static String getGMTDate() {
		Date date = new Date();
		return formatGMT(date);
	}

	/**
	 * 格式化GMT日期
	 *
	 * @param date
	 * @return
	 */
	public static String formatGMT(Date date) {
		DateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
		format.setTimeZone(TimeZone.getTimeZone("GMT"));
		return format.format(date);
	}
}

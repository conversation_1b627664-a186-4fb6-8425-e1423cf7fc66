package com.pitaya.devdiscovery.aiability.utils;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;

@Component
public class InternalRagUtils {
    @Autowired
    private HttpUtils httpUtils;
    @Value("${ai.ability.rag.url}")
    private String aiAbilityRagUrl;

    /**
     * 调用rag接口
     *
     * @param knowId
     * @param query
     * @return
     */
    public String callRagApi(String knowId, String query) {
        try {
            String response = callRagApi(knowId, 1, query);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 调用rag接口
     *
     * @param knowId
     * @param query
     * @return
     */
    public String callRagApi(String knowId, Integer topKey, String query) {
        try {
            JSONObject request = new JSONObject();
            request.put("knowledge_base_name", knowId);
            request.put("query", query);
            request.put("history", new ArrayList<>());
            request.put("top_k", topKey);
            request.put("stream", false);
            request.put("user_id", "dingh9");
            String response = httpUtils.postData(aiAbilityRagUrl, JSONObject.toJSONString(request));
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析rag返回的信息，获取pageContent数组
     *
     * @param response rag返回的响应
     * @return pageContent字符串列表
     */
    public List<String> parseRagResponseContents(String response) {
        return parseRagResponseContents(response, null);
    }

    /**
     * 解析rag返回的信息，根据文件名过滤并获取
     */
    public List<String> parseRagResponseContents(String response, String fileNameFilter) {
        List<String> contentList = new ArrayList<>();
        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (!response.contains("未找到相关文档") && jsonObject.containsKey("data") &&  jsonObject.getJSONArray("data") != null && jsonObject.getJSONArray("data").size() > 0) {
                List<JSONObject> dataArray = jsonObject.getJSONArray("data").toJavaList(JSONObject.class);
                for (JSONObject item : dataArray) {
                    String fileName = item.getString("fileName");
                    String pageContent = item.getString("pageContent");
                    if (fileNameFilter == null || (fileName != null && fileName.contains(fileNameFilter))) {
                        contentList.add(pageContent);
                    }
                }
            }
            return contentList;
        } catch (Exception e) {
            e.printStackTrace();
            return contentList;
        }
    }

    public boolean hasValidRagResponse(String response) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.containsKey("data")) {
                List<Object> dataArray = jsonObject.getJSONArray("data");
                if (dataArray.size() == 1) {
                    Object firstItem = dataArray.get(0);
                    if (firstItem instanceof String) {
                        String content = (String) firstItem;
                        return !content.contains("未找到相关文档");
                    }
                }
                return !dataArray.isEmpty();
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}

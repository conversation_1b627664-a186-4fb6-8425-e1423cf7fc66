package com.pitaya.devdiscovery.aiability.utils;

/**
 * @program: ds-recommend-product
 * @ClassName TextSimilarityUtils
 * @description:
 * @author: ma<PERSON><PERSON>
 * @date: 2025-04-21 15:32
 * @Version 1.0
 **/
public class TextSimilarityUtils {
    /**
     * 计算两个字符串的Levenshtein相似度（基于编辑距离）
     * @param a 字符串A
     * @param b 字符串B
     * @return 相似度百分比（0.0~1.0）
     * @throws IllegalArgumentException 输入为null时抛出异常
     */
    public static double similarity(String a, String b) {
        if (a == null || b == null) {
            throw new IllegalArgumentException("Input cannot be null");
        }
        int[][] dp = new int[a.length()+1][b.length()+1];
        for (int i=0; i<=a.length(); i++) {
            dp[i][0] = i;
        };
        for (int j=0; j<=b.length(); j++) {
            dp[0][j] = j;
        };
        for (int i=1; i<=a.length(); i++) {
            for (int j=1; j<=b.length(); j++) {
                int cost = (a.charAt(i-1) == b.charAt(j-1)) ? 0 : 1;
                dp[i][j] = Math.min(Math.min(dp[i-1][j]+1, dp[i][j-1]+1), dp[i-1][j-1]+cost);
            }
        }
        int maxLen = Math.max(a.length(), b.length());
        return maxLen == 0 ? 1.0 : 1 - (double) dp[a.length()][b.length()] / maxLen;
    }
}

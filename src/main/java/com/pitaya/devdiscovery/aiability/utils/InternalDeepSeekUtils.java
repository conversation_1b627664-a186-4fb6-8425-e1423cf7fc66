package com.pitaya.devdiscovery.aiability.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * @program: dev-discovery
 * @ClassName 省内deepseek
 * @description:
 * @author: majian
 * @date: 2025-05-12 15:37
 * @Version 1.0
 **/
@Component
public class InternalDeepSeekUtils {
    private Logger logger = LoggerFactory.getLogger(InternalDeepSeekUtils.class);
    @Autowired
    private HttpUtils httpUtils;
    @Value("${ai.ability.ds.url}")
    private String abilityDsUrl;
    private final OkHttpClient okHttpClient = new OkHttpClient();
    public String ask(String prompt){
        try {
            JSONObject param = new JSONObject();
            param.put("user_id","zhaoy590");
            param.put("stream",true);
            param.put("model","qwen2");
            JSONObject message = new JSONObject();
            message.put("role","user");
            message.put("content",prompt);
            JSONArray messages = new JSONArray(){{add(message);}};
            param.put("messages",messages);
            logger.info("deepseek ask:"+JSONObject.toJSONString(param));
            String response = dsPostData(abilityDsUrl, JSONObject.toJSONString(param));
            logger.info("deepseek answer:"+response);
            response = response.replaceAll("(?s)<think>.*?</think>","");
            response = response.replaceFirst("^\\n","");
            response = response.replaceFirst("^\\n","");
            return response;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
    /**
     * 调用ds
     * @param url
     * @param jsonBody
     * @return
     * @throws IOException
     */
    public String dsPostData(String url, String jsonBody) throws IOException {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        StringBuilder fullResponse = new StringBuilder();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return "Error";
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.startsWith("data: ")) {
                        String jsonData = line.substring(5).trim();
                        if (jsonData.equals("[DONE]")) {
                            break;
                        } else {
                            String jsonStr = line.substring(6);
                            JSONObject json = JSONObject.parseObject(jsonStr);
                            JSONArray choices = json.getJSONArray("choices");
                            if (!choices.isEmpty()) {
                                JSONObject firstChoice = choices.getJSONObject(0);
                                JSONObject delta = firstChoice.getJSONObject("delta");
                                String content = delta.getString("content");
                                fullResponse.append(content);
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            return "Error";
        }
        return fullResponse.toString();
    }
}

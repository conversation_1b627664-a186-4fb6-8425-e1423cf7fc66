package com.pitaya.devdiscovery.aiability.utils;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class HMacHttpClientUtil {
    /**
     * 发送POST请求（带HMAC认证）
     *
     * @param url        请求URL
     * @param appKey     应用Key
     * @param appSecret  应用Secret
     * @param requestObj 请求对象
     * @return 响应结果
     */
    public String postWithHMacAuth(String url, String appKey, String appSecret, Object requestObj) {
        try {
            String requestBody = JSON.toJSONString(requestObj);
            log.info("发起HMAC认证POST请求: {}", url);
            log.info("请求体: {}", requestBody);
            UrlBuilder urlBuilder = UrlBuilder.of(url);
            String date = HMacAuthUtil.getGMTDate();
            String authorization = HMacAuthUtil.buildAuthorization(
                    urlBuilder,
                    "hmac-sha256",
                    appKey,
                    date,
                    appSecret,
                    Method.POST.name(),
                    urlBuilder.getPathStr(),
                    "HTTP/1.1");
            HttpRequest request = HttpRequest.post(url)
                    .header("Host", urlBuilder.getAuthority())
                    .header("Date", date)
                    .header("Authorization", authorization)
                    .body(requestBody)
                    .contentType("application/json");
            HttpResponse response = request.execute();
            String responseBody = response.body();
            log.info("响应状态码: {}", response.getStatus());
            log.info("响应结果: {}", responseBody);
            if (!response.isOk()) {
                throw new RuntimeException("HMAC认证HTTP请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            return responseBody;
        } catch (Exception e) {
            log.error("HMAC认证HTTP请求异常: {}", e.getMessage(), e);
            throw new RuntimeException("HMAC认证HTTP请求执行失败: " + e.getMessage(), e);
        }
    }
}

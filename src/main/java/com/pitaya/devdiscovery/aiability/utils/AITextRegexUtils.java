package com.pitaya.devdiscovery.aiability.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @program: dev-discovery
 * @ClassName AIStringHandingUtils
 * @description: ai响应字符串处理工具类
 * @author: ma<PERSON><PERSON>
 * @date: 2025-05-19 10:18
 * @Version 1.0
 **/
public class AITextRegexUtils {
    /**
     * 提取JSON内容
     * @param response API响应文本
     * @return 提取的JSON字符串
     */
    public static String extractJson(String response) {
        if (response == null || response.trim().isEmpty()) {
            return "{}";
        }
        String regex = "```json\\s*([\\s\\S]*?)\\s*```";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(response);
        if (matcher.find()) {
            String json = matcher.group(1).trim();
            return json;
        }
        return response.replaceAll("```json|```", "").trim();
    }
}

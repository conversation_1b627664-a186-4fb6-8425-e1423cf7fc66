package com.pitaya.devdiscovery.aiability.dao;

import com.pitaya.devdiscovery.aiability.entity.CustomerMatchLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName CustomerMatchLogMapper
 * @description: 客户匹配日志Mapper接口
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Mapper
public interface CustomerMatchLogMapper {

    /**
     * 批量插入客户匹配日志
     *
     * @param logs 日志列表
     * @return 插入成功的行数
     */
    int batchInsert(@Param("logs") List<CustomerMatchLog> logs);

    /**
     * 根据请求ID查询日志
     *
     * @param requestId 请求ID
     * @return 日志列表
     */
    List<CustomerMatchLog> selectByRequestId(@Param("requestId") String requestId);

    /**
     * 根据查询内容查询历史记录
     *
     * @param query 查询内容
     * @param limit 限制条数
     * @return 日志列表
     */
    List<CustomerMatchLog> selectByQuery(@Param("query") String query, @Param("limit") Integer limit);
}
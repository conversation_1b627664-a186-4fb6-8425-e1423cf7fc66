package com.pitaya.devdiscovery.aiability.service;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

public interface RagRecommendProductService {
    /**
     * 根据关键词查询关联产品
     * @param keywordsList
     * @return
     */
    LinkedHashSet<String> queryRecommendProductByKeywords(List<String> keywordsList);
    /**
     * 根据客户名称查询存量产品
     * @param customerName
     * @return
     */
    Set<String> queryExistingProductsByCustomerName(String customerName);
    /**
     * 基于编辑距离剔除存量产品
     * @param recommendProductSet
     * @param existProductSet
     * @param similarity
     * @return
     */
    LinkedHashSet<String> removeExistingProducts(LinkedHashSet<String> recommendProductSet,Set<String> existProductSet,Double similarity);
    /**
     * 调用ds描述过滤掉匹配度不高的产品
     */
    LinkedHashSet<String> filterProductsByDS(String description,List<String> keywords,LinkedHashSet<String> recommendProductSet);
}

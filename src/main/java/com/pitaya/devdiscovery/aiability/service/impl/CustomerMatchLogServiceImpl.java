package com.pitaya.devdiscovery.aiability.service.impl;

import com.pitaya.devdiscovery.aiability.dao.CustomerMatchLogMapper;
import com.pitaya.devdiscovery.aiability.dto.ragcustomermatch.IntentData;
import com.pitaya.devdiscovery.aiability.entity.CustomerMatchLog;
import com.pitaya.devdiscovery.aiability.service.CustomerMatchLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @program: dev-discovery
 * @ClassName CustomerMatchLogServiceImpl
 * @description: 客户匹配日志服务实现类
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Slf4j
@Service
public class CustomerMatchLogServiceImpl implements CustomerMatchLogService {

    @Autowired
    private CustomerMatchLogMapper customerMatchLogMapper;

    @Override
    public String saveMatchResults(String query, List<IntentData> matchResults) {
        try {
            if (matchResults == null || matchResults.isEmpty()) {
                log.warn("匹配结果为空，不进行保存");
                return null;
            }

            // 生成唯一的请求ID
            String requestId = generateRequestId();
            int resultCount = matchResults.size();

            // 转换为数据库实体
            List<CustomerMatchLog> logs = new ArrayList<>();
            for (IntentData intentData : matchResults) {
                CustomerMatchLog log = new CustomerMatchLog();
                log.setRequestId(requestId);
                log.setQuery(query);
                log.setAnswer(intentData.getAnswer());
                log.setIntent(intentData.getIntent());
                log.setQuestion(intentData.getQuestion());
                log.setReserve1(intentData.getReserve1());
                log.setReserve2(intentData.getReserve2());
                log.setReserve3(intentData.getReserve3());
                log.setReserve4(intentData.getReserve4());
                log.setReserve5(intentData.getReserve5());
                log.setScore(intentData.getScore());
                log.setResultCount(resultCount);
                logs.add(log);
            }

            // 批量插入数据库
            int insertCount = customerMatchLogMapper.batchInsert(logs);
            log.info("成功保存客户匹配日志，请求ID: {}, 保存条数: {}", requestId, insertCount);

            return requestId;

        } catch (Exception e) {
            log.error("保存客户匹配日志失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存客户匹配日志失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<CustomerMatchLog> getMatchResultsByRequestId(String requestId) {
        try {
            return customerMatchLogMapper.selectByRequestId(requestId);
        } catch (Exception e) {
            log.error("根据请求ID查询匹配记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询匹配记录失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<CustomerMatchLog> getHistoryByQuery(String query, Integer limit) {
        try {
            return customerMatchLogMapper.selectByQuery(query, limit);
        } catch (Exception e) {
            log.error("根据查询内容查询历史记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询历史记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成唯一的请求ID
     * 格式: REQ_yyyyMMdd_HHmmss_UUID前8位
     */
    private String generateRequestId() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "REQ_" + timestamp + "_" + uuid;
    }
}
package com.pitaya.devdiscovery.aiability.service;

import com.pitaya.devdiscovery.aiability.dto.ragcustomermatch.IntentData;
import com.pitaya.devdiscovery.aiability.entity.CustomerMatchLog;

import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName CustomerMatchLogService
 * @description: 客户匹配日志服务接口
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
public interface CustomerMatchLogService {

    /**
     * 保存客户匹配结果
     *
     * @param query        查询内容
     * @param matchResults 匹配结果列表
     * @return 请求ID
     */
    String saveMatchResults(String query, List<IntentData> matchResults);

    /**
     * 根据请求ID查询匹配记录
     *
     * @param requestId 请求ID
     * @return 匹配记录列表
     */
    List<CustomerMatchLog> getMatchResultsByRequestId(String requestId);

    /**
     * 根据查询内容查询历史记录
     *
     * @param query 查询内容
     * @param limit 限制条数
     * @return 历史记录列表
     */
    List<CustomerMatchLog> getHistoryByQuery(String query, Integer limit);
}
package com.pitaya.devdiscovery.aiability.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.aiability.config.IntentRecognitionProperties;
import com.pitaya.devdiscovery.aiability.dto.ragcustomermatch.IntentData;
import com.pitaya.devdiscovery.aiability.dto.ragcustomermatch.IntentRecognitionRequest;
import com.pitaya.devdiscovery.aiability.service.RagCustomerMatchService;
import com.pitaya.devdiscovery.aiability.utils.HMacHttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName RagCustomerMatchServiceImpl
 * @description: RAG客户匹配服务实现类
 * @author: ma<PERSON>an
 * @date: 2025-06-04 14:41
 * @Version 1.0
 **/
@Slf4j
@Service
public class RagCustomerMatchServiceImpl implements RagCustomerMatchService {

    @Autowired
    private HMacHttpClientUtil hMacHttpClientUtil;

    @Autowired
    private IntentRecognitionProperties intentRecognitionProperties;

    @Override
    public List<IntentData> matchCustomers(String query) {
        try {
            log.info("开始匹配客户，查询内容: {}", query);
            IntentRecognitionRequest request = IntentRecognitionRequest.createCustomerMatchRequest(query);
            String responseJson = hMacHttpClientUtil.postWithHMacAuth(
                    intentRecognitionProperties.getIntentRecognitionUrl(),
                    intentRecognitionProperties.getAppKey(),
                    intentRecognitionProperties.getAppSecret(),
                    request);
            JSONObject responseObject = JSON.parseObject(responseJson);
            if (responseObject == null) {
                log.warn("API响应解析失败，返回空结果 (无法解析顶层JSON对象)");
                return Collections.emptyList();
            }
            Integer code = responseObject.getInteger("code");
            if (code == null || !code.equals(200)) {
                log.warn("API调用失败，响应码: {}", code);
                return Collections.emptyList();
            }
            JSONArray dataArray = responseObject.getJSONArray("data");
            if (dataArray == null || dataArray.isEmpty()) {
                log.info("API返回的data节点为空或不存在");
                return Collections.emptyList();
            }
            List<IntentData> results = dataArray.toList(IntentData.class);
            log.info("客户匹配完成，匹配到 {} 条结果", results.size());
            return results;
        } catch (Exception e) {
            log.error("客户匹配异常: {}", e.getMessage(), e);
            throw new RuntimeException("客户匹配失败: " + e.getMessage(), e);
        }
    }
}

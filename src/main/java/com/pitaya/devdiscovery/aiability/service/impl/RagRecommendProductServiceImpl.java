package com.pitaya.devdiscovery.aiability.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.aiability.constant.RagKnowledgeConstant;
import com.pitaya.devdiscovery.aiability.service.RagRecommendProductService;
import com.pitaya.devdiscovery.aiability.utils.AITextRegexUtils;
import com.pitaya.devdiscovery.aiability.utils.InternalDeepSeekUtils;
import com.pitaya.devdiscovery.aiability.utils.InternalRagUtils;
import com.pitaya.devdiscovery.aiability.utils.TextSimilarityUtils;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.utils.RegularAnalysisUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: dev-discovery
 * @ClassName RagRecommendProductServiceImpl
 * @description: rag 查询推荐产品
 * @author: majian
 * @date: 2025-05-19 10:32
 * @Version 1.0
 **/
@Service
public class RagRecommendProductServiceImpl implements RagRecommendProductService {
    private static final String EXISTING_PRODUCTS_SPLIT_REGEX = ";";
    @Autowired
    private InternalRagUtils internalRagUtils;
    @Autowired
    private InternalDeepSeekUtils internalDeepSeekUtils;
    private Logger logger = LoggerFactory.getLogger(RagRecommendProductServiceImpl.class);
    @Override
    public LinkedHashSet<String> queryRecommendProductByKeywords(List<String> keywordsList) {
        LinkedHashSet<String> recommendProductSet = new LinkedHashSet<>();
        for (String keyword : keywordsList) {
            String query = new StringBuilder("查询").append(keyword).append("相关产品").toString();
            String response = internalRagUtils.callRagApi(RagKnowledgeConstant.PRODUCT_RECOMMEND_KNOWID, query);
            List<String> pageContentList = internalRagUtils.parseRagResponseContents(response, "产品");
            try {
                int recommendNum = 0;
                for (int i = 0; i < pageContentList.size(); i++) {
                    String pageContent = pageContentList.get(i);
                    String productName = RegularAnalysisUtils.parsingProductName(pageContent);
                    if (StringUtils.isNotEmpty(productName)) {
                        logger.info("关键词：{} 产品名称：{}", keyword, productName);
                        if (!recommendProductSet.contains(productName)) {
                            recommendProductSet.add(productName);
                            recommendNum++;
                        }
                        if (recommendNum >= 3) {
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                continue;
            }
        }
        return recommendProductSet;
    }

    /**
     * 根据客户名称查询存量产品
     * @param customerName
     * @return
     */
    @Override
    public Set<String> queryExistingProductsByCustomerName(String customerName) {
        String query = new StringBuilder("查询").append(customerName).append("存量业务").toString();
        String response = internalRagUtils.callRagApi(RagKnowledgeConstant.PRODUCT_RECOMMEND_KNOWID, query);
        if (response.contains("未找到相关文档")) {
            return new HashSet<>();
        }
        HashSet<String> existProductSet = new HashSet<>();
        try {
            JSONObject responseJSON = JSONObject.parseObject(response);
            JSONArray array = responseJSON.getJSONArray("data");
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                if (obj.containsKey("pageContent")) {
                    String pageContent = obj.getString("pageContent");
                    String productName = RegularAnalysisUtils.parsingExistProductName(pageContent);
                    String productType = RegularAnalysisUtils.parsingExistProductType(pageContent);
                    if (StringUtils.isNotEmpty(productName)) {
                        Arrays.stream(productName.split(EXISTING_PRODUCTS_SPLIT_REGEX)).forEach(item -> {
                            existProductSet.add(item);
                        });
                    }
                    if (StringUtils.isNotEmpty(productType)) {
                        Arrays.stream(productType.split(EXISTING_PRODUCTS_SPLIT_REGEX)).forEach(item -> {
                            existProductSet.add(item);
                        });
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return existProductSet;
    }

    /**
     * 基于编辑距离剔除存量产品
     * @param recommendProductSet
     * @param existProductSet
     * @param similarity
     * @return
     */
    @Override
    public LinkedHashSet<String> removeExistingProducts(LinkedHashSet<String> recommendProductSet, Set<String> existProductSet, Double similarity) {
        LinkedHashSet<String> resultProduct = new LinkedHashSet<>();
        if (existProductSet.size() == 0) {
            return recommendProductSet;
        } else {
            recommendProductSet.stream().forEach(item -> {
                boolean isNoSimilarity = true;
                for (String existProduct : existProductSet) {
                    double itemSimilar = TextSimilarityUtils.similarity(item, existProduct);
                    //logger.info("产品名称：{} 存量产品名称：{} 相似度：{}", item, existProduct, itemSimilar);
                    if (itemSimilar >= similarity) {
                        isNoSimilarity = false;
                        break;
                    }
                }
                if (isNoSimilarity) {
                    resultProduct.add(item);
                }
            });
        }
        return resultProduct;
    }

    @Override
    public LinkedHashSet<String> filterProductsByDS(String description, List<String> keywords, LinkedHashSet<String> recommendProductSet) {
       if (recommendProductSet == null || recommendProductSet.size() == 0){
           return recommendProductSet;
       }
        String keywordsStr = keywords.stream().collect(Collectors.joining(","));
        String productStr = recommendProductSet.stream().collect(Collectors.joining(","));
        StringBuffer prompt = new StringBuffer();
        prompt.append("你是一个行业产品匹配专家。\n");
        prompt.append("你的任务是根据用户需求描述，从初步匹配的产品列表中进行筛选，着重考虑产品的**行业相关性和功能匹配度**。\n");
        prompt.append("目标是找出用户**当前行业或相近行业中，能直接满足或经过简单调整即可满足**其需求的产品。\n");
        prompt.append("坚决排除与用户**当前行业明显不符，且功能关联性弱**的产品。\n\n");
        prompt.append("**用户需求描述：** ").append(description).append("\n\n");
        prompt.append("**初步匹配的产品列表：**").append(productStr).append("\n\n");
        prompt.append("**请你执行以下步骤：**\n");
        prompt.append("1. **需求拆解：** 细致拆解用户需求，提取关键信息，包括所属行业、核心功能、解决问题和预期效果。\n");
        prompt.append("2. **行业评估：** 评估初步匹配的每个产品所属行业与用户行业的相关性，**优先考虑同一行业或上下游行业的产品**。\n");
        prompt.append("3. **功能匹配判断：**\n");
        prompt.append("    *  如果产品与用户**属于同一行业或相近行业**，且其核心功能与用户需求**高度匹配**，则保留。\n");
        prompt.append("    *  如果产品与用户**属于同一行业或相近行业**，但核心功能需要**大幅修改或扩展**才能满足用户需求，则需要谨慎评估，只有在修改成本较低且可行性高的情况下才保留。\n");
        prompt.append("   *   如果产品与用户**行业明显不符**，即使功能上有少量关联，也应**谨慎排除**，除非产品的功能是通用性极强，且在其他行业也有广泛应用。\n");
        prompt.append("   *   **明确提示：** 在莱西七星河乡村振兴项目背景下，**一般情况下不应考虑矿山行业的产品**。\n");
        prompt.append("4. **JSON 输出：** 将所有筛选出的产品以 JSON 格式输出。\n\n");
        prompt.append("**输出要求：**\n");
        prompt.append("*   重点输出**行业相关、功能匹配**的产品。\n");
        prompt.append("*   慎重考虑跨行业产品，**除非有特别充分的理由**。\n");
        prompt.append("*   明确排除明显不符合行业背景的产品。\n");
        prompt.append("*   JSON 格式必须**严格**按照给定的格式。\n");
        prompt.append("*   **不包含**任何分析、解释性文字或其他非 JSON 内容。\n\n");
        prompt.append("**输出示例：**\n");
        prompt.append("```json\n");
        prompt.append("{\n");
        prompt.append("    \"product\": [\"产品1\", \"产品2\"]\n");
        prompt.append("}\n");
        prompt.append("```");
        String answer = AITextRegexUtils.extractJson(internalDeepSeekUtils.ask(prompt.toString()));
        JSONObject answerObj = JSONObject.parseObject(answer);
        LinkedHashSet<String> productSet = new LinkedHashSet<>();
        if(answerObj.containsKey("product") && answerObj.getJSONArray("product") != null){
            List<String> productList = answerObj.getJSONArray("product").toJavaList(String.class);
            productSet = new LinkedHashSet<>(productList);
        }
        return productSet;
    }
}

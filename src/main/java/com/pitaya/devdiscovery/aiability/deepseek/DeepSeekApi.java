package com.pitaya.devdiscovery.aiability.deepseek;

import com.alibaba.fastjson2.JSON;
import com.pitaya.devdiscovery.aiability.deepseek.constant.DeepSeekConstants;
import com.pitaya.devdiscovery.aiability.deepseek.dto.ChatCompletionRequest;
import com.pitaya.devdiscovery.aiability.deepseek.dto.ChatCompletionResponse;
import com.pitaya.devdiscovery.aiability.deepseek.exception.DeepSeekApiException;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
public class DeepSeekApi {

    private final OkHttpClient client;
    private final String apiKey;
    private final String baseUrl;

    /**
     * 创建DeepSeekApi实例
     *
     * @param apiKey DeepSeek API密钥
     */
    public DeepSeekApi(String apiKey) {
        this(apiKey, DeepSeekConstants.API_BASE_URL);
    }

    /**
     * 创建DeepSeekApi实例
     *
     * @param apiKey  DeepSeek API密钥
     * @param baseUrl DeepSeek API基础URL
     */
    public DeepSeekApi(String apiKey, String baseUrl) {
        this.apiKey = apiKey;
        this.baseUrl = baseUrl;

        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 使用深度思考模式调用DeepSeek V3模型(非流式)
     *
     * @param userPrompt 用户提问内容
     * @return AI回复内容
     */
    public String callWithDeepThinking(String userPrompt) {
        return callWithDeepThinking(userPrompt, DeepSeekConstants.MODEL_DEEPSEEK_CHAT);
    }

    /**
     * 使用深度思考模式调用指定DeepSeek模型(非流式)
     *
     * @param userPrompt 用户提问内容
     * @param modelName  模型名称
     * @return AI回复内容
     */
    public String callWithDeepThinking(String userPrompt, String modelName) {
        return call(ChatParams.builder()
                .model(modelName)
                .messages(buildDeepThinkingMessages(userPrompt))
                .temperature(0.7)
                .maxTokens(4000)
                .build());
    }

    /**
     * 调用DeepSeek API (自定义参数)
     *
     * @param params 自定义调用参数
     * @return AI回复内容
     */
    public String call(ChatParams params) {
        List<ChatCompletionRequest.Message> messages = params.getMessages();
        if (messages == null || messages.isEmpty()) {
            throw new IllegalArgumentException("消息列表不能为空");
        }

        ChatCompletionRequest.ChatCompletionRequestBuilder requestBuilder = ChatCompletionRequest.builder()
                .model(params.getModel())
                .messages(messages)
                .temperature(params.getTemperature())
                .max_tokens(params.getMaxTokens())
                .stream(false); // 非流式调用

        ChatCompletionRequest request = requestBuilder.build();

        try {
            ChatCompletionResponse response = sendRequest(request);
            if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                return response.getChoices().get(0).getMessage().getContent();
            } else {
                throw new DeepSeekApiException("API响应中没有内容");
            }
        } catch (IOException e) {
            log.error("调用DeepSeek API失败", e);
            throw new DeepSeekApiException("调用DeepSeek API失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建添加了深度思考系统提示的消息列表
     *
     * @param userPrompt 用户提问内容
     * @return 消息列表
     */
    private List<ChatCompletionRequest.Message> buildDeepThinkingMessages(String userPrompt) {
        List<ChatCompletionRequest.Message> messages = new ArrayList<>();

        // 添加系统消息，启用深度思考模式
        messages.add(ChatCompletionRequest.Message.builder()
                .role(DeepSeekConstants.ROLE_SYSTEM)
                .content(DeepSeekConstants.DEEP_THINKING_PROMPT)
                .build());

        // 添加用户消息
        messages.add(ChatCompletionRequest.Message.builder()
                .role(DeepSeekConstants.ROLE_USER)
                .content(userPrompt)
                .build());

        return messages;
    }

    /**
     * 发送请求到DeepSeek API
     *
     * @param request 请求对象
     * @return 响应对象
     * @throws IOException 发生IO异常时抛出
     */
    private ChatCompletionResponse sendRequest(ChatCompletionRequest request) throws IOException {
        String url = baseUrl + DeepSeekConstants.V1_CHAT_COMPLETIONS_ENDPOINT;

        RequestBody body = RequestBody.create(
                JSON.toJSONString(request),
                MediaType.parse("application/json; charset=utf-8"));

        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();

        try (Response response = client.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                if (response.body() != null) {
                    String errorBody = response.body().string();
                    log.error("DeepSeek API错误: {}, 状态码: {}", errorBody, response.code());
                }
                throw new DeepSeekApiException(response.code(), "DeepSeek API调用失败，状态码: " + response.code());
            }

            if (response.body() != null) {
                String responseBody = response.body().string();
                return JSON.parseObject(responseBody, ChatCompletionResponse.class);
            } else {
                throw new DeepSeekApiException("API响应体为空");
            }
        }
    }

    /**
     * 聊天参数配置类
     */
    @Data
    @Builder
    public static class ChatParams {
        @Builder.Default
        private String model = DeepSeekConstants.MODEL_DEEPSEEK_CHAT;

        private List<ChatCompletionRequest.Message> messages;

        @Builder.Default
        private double temperature = 0.7;

        @Builder.Default
        private int maxTokens = 4000;
    }
}

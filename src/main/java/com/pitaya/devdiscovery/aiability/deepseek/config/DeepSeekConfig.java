package com.pitaya.devdiscovery.aiability.deepseek.config;

import com.pitaya.devdiscovery.aiability.deepseek.DeepSeekApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DeepSeekConfig {

    @Value("${deepseek.api.key:}")
    private String apiKey;

    @Value("${deepseek.api.base-url:https://api.deepseek.com}")
    private String baseUrl;

    @Bean
    public DeepSeekApi deepSeekApi() {
        return new DeepSeekApi(apiKey, baseUrl);
    }
}

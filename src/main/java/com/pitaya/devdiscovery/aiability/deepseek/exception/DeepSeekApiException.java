package com.pitaya.devdiscovery.aiability.deepseek.exception;

public class DeepSeekApiException extends RuntimeException {

    private int statusCode;

    public DeepSeekApiException(String message) {
        super(message);
    }

    public DeepSeekApiException(String message, Throwable cause) {
        super(message, cause);
    }

    public DeepSeekApiException(int statusCode, String message) {
        super(message);
        this.statusCode = statusCode;
    }

    public int getStatusCode() {
        return statusCode;
    }
}

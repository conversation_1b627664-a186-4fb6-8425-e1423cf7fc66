package com.pitaya.devdiscovery.aiability.deepseek.util;

import com.pitaya.devdiscovery.aiability.deepseek.DeepSeekApi;
import com.pitaya.devdiscovery.aiability.deepseek.constant.DeepSeekConstants;
import com.pitaya.devdiscovery.aiability.deepseek.dto.ChatCompletionRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class DeepSeekUtils {

    private final DeepSeekApi deepSeekApi;

    @Autowired
    public DeepSeekUtils(DeepSeekApi deepSeekApi) {
        this.deepSeekApi = deepSeekApi;
    }

    /**
     * 使用DeepSeek V3模型，以深度思考模式进行问答
     *
     * @param question 用户问题
     * @return AI回答
     */
    public String ask(String question) {
        return deepSeekApi.callWithDeepThinking(question);
    }

    /**
     * 使用指定的模型，以深度思考模式进行问答
     *
     * @param question 用户问题
     * @param model    模型名称，例如：deepseek-chat, deepseek-reasoner
     * @return AI回答
     */
    public String ask(String question, String model) {
        return deepSeekApi.callWithDeepThinking(question, model);
    }

    /**
     * 使用自定义消息列表调用DeepSeek API
     *
     * @param messages 消息列表
     * @return AI回答
     */
    public String chat(List<ChatCompletionRequest.Message> messages) {
        return deepSeekApi.call(DeepSeekApi.ChatParams.builder()
                .model(DeepSeekConstants.MODEL_DEEPSEEK_CHAT)
                .messages(messages)
                .build());
    }

    /**
     * 使用自定义参数调用DeepSeek API
     *
     * @param model       模型名称
     * @param messages    消息列表
     * @param temperature 温度参数
     * @param maxTokens   最大token数
     * @return AI回答
     */
    public String chat(String model, List<ChatCompletionRequest.Message> messages,
            double temperature, int maxTokens) {
        return deepSeekApi.call(DeepSeekApi.ChatParams.builder()
                .model(model)
                .messages(messages)
                .temperature(temperature)
                .maxTokens(maxTokens)
                .build());
    }

    /**
     * 创建系统消息
     *
     * @param content 消息内容
     * @return 系统消息对象
     */
    public ChatCompletionRequest.Message systemMessage(String content) {
        return ChatCompletionRequest.Message.builder()
                .role(DeepSeekConstants.ROLE_SYSTEM)
                .content(content)
                .build();
    }

    /**
     * 创建用户消息
     *
     * @param content 消息内容
     * @return 用户消息对象
     */
    public ChatCompletionRequest.Message userMessage(String content) {
        return ChatCompletionRequest.Message.builder()
                .role(DeepSeekConstants.ROLE_USER)
                .content(content)
                .build();
    }

    /**
     * 创建助手消息
     *
     * @param content 消息内容
     * @return 助手消息对象
     */
    public ChatCompletionRequest.Message assistantMessage(String content) {
        return ChatCompletionRequest.Message.builder()
                .role(DeepSeekConstants.ROLE_ASSISTANT)
                .content(content)
                .build();
    }
}

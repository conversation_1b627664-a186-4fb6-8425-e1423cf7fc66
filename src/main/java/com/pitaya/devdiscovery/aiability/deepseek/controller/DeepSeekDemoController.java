package com.pitaya.devdiscovery.aiability.deepseek.controller;

import com.pitaya.devdiscovery.aiability.deepseek.constant.DeepSeekConstants;
import com.pitaya.devdiscovery.aiability.deepseek.dto.ChatCompletionRequest;
import com.pitaya.devdiscovery.aiability.deepseek.util.DeepSeekUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/deepseek")
public class DeepSeekDemoController {

    private final DeepSeekUtils deepSeekUtils;

    @Autowired
    public DeepSeekDemoController(DeepSeekUtils deepSeekUtils) {
        this.deepSeekUtils = deepSeekUtils;
    }

    /**
     * 使用深度思考模式问答（V3模型）
     *
     * @param question 用户问题
     * @return AI回答
     */
    @PostMapping("/ask")
    public Map<String, Object> ask(@RequestParam String question) {
        log.info("接收到问题: {}", question);
        String answer = deepSeekUtils.ask(question);

        Map<String, Object> result = new HashMap<>();
        result.put("question", question);
        result.put("answer", answer);
        result.put("model", DeepSeekConstants.MODEL_DEEPSEEK_CHAT);

        return result;
    }

    /**
     * 使用推理模型问答（DeepSeek-R1）
     *
     * @param question 用户问题
     * @return AI回答
     */
    @PostMapping("/ask-reasoner")
    public Map<String, Object> askReasoner(@RequestParam String question) {
        log.info("接收到推理问题: {}", question);
        String answer = deepSeekUtils.ask(question, DeepSeekConstants.MODEL_DEEPSEEK_REASONER);

        Map<String, Object> result = new HashMap<>();
        result.put("question", question);
        result.put("answer", answer);
        result.put("model", DeepSeekConstants.MODEL_DEEPSEEK_REASONER);

        return result;
    }

    /**
     * 自定义对话参数
     */
    @PostMapping("/chat")
    public Map<String, Object> chat(@RequestBody CustomChatRequest request) {
        log.info("接收到自定义对话请求: {}", request);

        List<ChatCompletionRequest.Message> messages = new ArrayList<>();

        // 添加系统消息（如果有）
        if (request.getSystemPrompt() != null && !request.getSystemPrompt().isEmpty()) {
            messages.add(deepSeekUtils.systemMessage(request.getSystemPrompt()));
        }

        // 添加用户消息
        messages.add(deepSeekUtils.userMessage(request.getUserPrompt()));

        // 发送请求
        String answer = deepSeekUtils.chat(
                request.getModel(),
                messages,
                request.getTemperature(),
                request.getMaxTokens());

        Map<String, Object> result = new HashMap<>();
        result.put("prompt", request.getUserPrompt());
        result.put("answer", answer);
        result.put("model", request.getModel());

        return result;
    }

    /**
     * 自定义对话请求
     */
    public static class CustomChatRequest {
        private String model = DeepSeekConstants.MODEL_DEEPSEEK_CHAT;
        private String systemPrompt;
        private String userPrompt;
        private double temperature = 0.7;
        private int maxTokens = 4000;

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public String getSystemPrompt() {
            return systemPrompt;
        }

        public void setSystemPrompt(String systemPrompt) {
            this.systemPrompt = systemPrompt;
        }

        public String getUserPrompt() {
            return userPrompt;
        }

        public void setUserPrompt(String userPrompt) {
            this.userPrompt = userPrompt;
        }

        public double getTemperature() {
            return temperature;
        }

        public void setTemperature(double temperature) {
            this.temperature = temperature;
        }

        public int getMaxTokens() {
            return maxTokens;
        }

        public void setMaxTokens(int maxTokens) {
            this.maxTokens = maxTokens;
        }

        @Override
        public String toString() {
            return "CustomChatRequest{" +
                    "model='" + model + '\'' +
                    ", userPrompt='" + userPrompt + '\'' +
                    ", temperature=" + temperature +
                    ", maxTokens=" + maxTokens +
                    '}';
        }
    }
}

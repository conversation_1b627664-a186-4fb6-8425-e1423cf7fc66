package com.pitaya.devdiscovery.aiability.controller;

import com.pitaya.devdiscovery.aiability.dto.BatchQueryRequest;
import com.pitaya.devdiscovery.aiability.dto.QueryRequest;
import com.pitaya.devdiscovery.aiability.dto.ragcustomermatch.IntentData;
import com.pitaya.devdiscovery.aiability.entity.CustomerMatchLog;
import com.pitaya.devdiscovery.aiability.service.CustomerMatchLogService;
import com.pitaya.devdiscovery.aiability.service.RagCustomerMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @program: dev-discovery
 * @ClassName TestMatchController
 * @description: 用于测试客户匹配服务的Controller
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/aiability/test-match")
public class TestMatchController {

    @Autowired
    private RagCustomerMatchService ragCustomerMatchService;

    @Autowired
    private CustomerMatchLogService customerMatchLogService;

    /**
     * 测试客户匹配功能 (POST)
     *
     * @param request 包含查询字符串的对象 (e.g., {"query": "公司名称"})
     * @return 匹配到的客户列表或错误信息
     */
    @PostMapping("/customers")
    public ResponseEntity<Map<String, Object>> testMatchCustomers(@RequestBody QueryRequest request) {
        Map<String, Object> response = new HashMap<>();
        String query = request.getQuery();
        String requestId = null;

        try {
            log.info("测试接口 /customers (POST) 被调用, 查询参数: {}", query);
            if (query == null || query.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "查询参数 'query' 不能为空");
                response.put("data", Collections.emptyList());
                return ResponseEntity.badRequest().body(response);
            }

            // 调用客户匹配服务
            List<IntentData> matchedCustomers = ragCustomerMatchService.matchCustomers(query);

            // 保存匹配结果到数据库
            try {
                requestId = customerMatchLogService.saveMatchResults(query, matchedCustomers);
                log.info("成功保存匹配结果到数据库，请求ID: {}", requestId);
            } catch (Exception e) {
                log.error("保存匹配结果到数据库失败: {}", e.getMessage(), e);
                // 即使保存失败，也继续返回匹配结果，只记录警告
            }

            response.put("success", true);
            response.put("message", "客户匹配成功");
            response.put("count", matchedCustomers.size());
            response.put("data", matchedCustomers);
            response.put("requestId", requestId); // 返回请求ID，方便后续查询
            log.info("成功匹配到 {} 个客户", matchedCustomers.size());
            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            log.error("客户匹配服务调用异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "服务内部错误: " + e.getMessage());
            response.put("data", Collections.emptyList());
            response.put("requestId", requestId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        } catch (Exception e) {
            log.error("处理客户匹配请求时发生未知异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "请求处理时发生未知错误");
            response.put("data", Collections.emptyList());
            response.put("requestId", requestId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量客户匹配功能
     *
     * @param request 包含逗号分隔查询字符串的对象 (e.g., {"queries": "自然资源,国土,物流"})
     * @return 批量匹配结果
     */
    @PostMapping("/customers/batch")
    public ResponseEntity<Map<String, Object>> batchMatchCustomers(@RequestBody BatchQueryRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("批量匹配接口被调用, 查询参数: {}", request.getQueries());

            // 参数验证
            if (!StringUtils.hasText(request.getQueries())) {
                response.put("success", false);
                response.put("message", "查询参数 'queries' 不能为空");
                response.put("totalQueries", 0);
                response.put("results", Collections.emptyList());
                return ResponseEntity.badRequest().body(response);
            }

            // 解析查询内容
            String[] queryArray = request.getQueries().split(",");
            List<String> validQueries = new ArrayList<>();

            // 过滤和清理查询内容
            for (String query : queryArray) {
                String trimmedQuery = query.trim();
                if (StringUtils.hasText(trimmedQuery)) {
                    validQueries.add(trimmedQuery);
                }
            }

            if (validQueries.isEmpty()) {
                response.put("success", false);
                response.put("message", "没有有效的查询内容");
                response.put("totalQueries", 0);
                response.put("results", Collections.emptyList());
                return ResponseEntity.badRequest().body(response);
            }

            log.info("解析到 {} 个有效查询: {}", validQueries.size(), validQueries);

            // 批量处理
            List<Map<String, Object>> results = new ArrayList<>();
            int successCount = 0;
            int failCount = 0;

            for (String query : validQueries) {
                Map<String, Object> queryResult = new HashMap<>();
                queryResult.put("query", query);

                try {
                    // 调用匹配服务
                    List<IntentData> matchedCustomers = ragCustomerMatchService.matchCustomers(query);

                    // 保存到数据库
                    String requestId = null;
                    try {
                        requestId = customerMatchLogService.saveMatchResults(query, matchedCustomers);
                        log.info("查询 '{}' 成功保存到数据库，请求ID: {}", query, requestId);
                    } catch (Exception e) {
                        log.error("查询 '{}' 保存到数据库失败: {}", query, e.getMessage());
                        // 继续处理，不因保存失败而中断
                    }

                    queryResult.put("success", true);
                    queryResult.put("count", matchedCustomers.size());
                    queryResult.put("requestId", requestId);

                    // 根据includeDetails决定是否返回详细数据
                    if (Boolean.TRUE.equals(request.getIncludeDetails())) {
                        queryResult.put("data", matchedCustomers);
                    }

                    successCount++;
                    log.info("查询 '{}' 成功，匹配到 {} 条结果", query, matchedCustomers.size());

                } catch (Exception e) {
                    log.error("查询 '{}' 失败: {}", query, e.getMessage(), e);
                    queryResult.put("success", false);
                    queryResult.put("count", 0);
                    queryResult.put("error", e.getMessage());
                    queryResult.put("requestId", null);
                    failCount++;
                }

                results.add(queryResult);
            }

            // 构建响应
            response.put("success", true);
            response.put("message", String.format("批量查询完成，成功 %d 个，失败 %d 个", successCount, failCount));
            response.put("totalQueries", validQueries.size());
            response.put("successCount", successCount);
            response.put("failCount", failCount);
            response.put("results", results);

            log.info("批量匹配完成，总查询数: {}, 成功: {}, 失败: {}", validQueries.size(), successCount, failCount);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("批量匹配处理异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "批量处理时发生错误: " + e.getMessage());
            response.put("totalQueries", 0);
            response.put("results", Collections.emptyList());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 根据请求ID查询历史匹配记录
     *
     * @param requestId 请求ID
     * @return 历史匹配记录
     */
    @GetMapping("/history/{requestId}")
    public ResponseEntity<Map<String, Object>> getHistoryByRequestId(@PathVariable String requestId) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("查询历史记录接口被调用, 请求ID: {}", requestId);

            List<CustomerMatchLog> historyLogs = customerMatchLogService.getMatchResultsByRequestId(requestId);

            response.put("success", true);
            response.put("message", "查询历史记录成功");
            response.put("count", historyLogs.size());
            response.put("data", historyLogs);
            response.put("requestId", requestId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("查询历史记录异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询历史记录失败: " + e.getMessage());
            response.put("data", Collections.emptyList());
            response.put("requestId", requestId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 根据查询内容查询历史记录
     *
     * @param query 查询内容
     * @param limit 限制条数（可选，默认10）
     * @return 历史记录
     */
    @GetMapping("/history")
    public ResponseEntity<Map<String, Object>> getHistoryByQuery(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") Integer limit) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("按查询内容查询历史记录接口被调用, 查询内容: {}, 限制条数: {}", query, limit);

            List<CustomerMatchLog> historyLogs = customerMatchLogService.getHistoryByQuery(query, limit);

            response.put("success", true);
            response.put("message", "查询历史记录成功");
            response.put("count", historyLogs.size());
            response.put("data", historyLogs);
            response.put("query", query);
            response.put("limit", limit);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("按查询内容查询历史记录异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询历史记录失败: " + e.getMessage());
            response.put("data", Collections.emptyList());
            response.put("query", query);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}

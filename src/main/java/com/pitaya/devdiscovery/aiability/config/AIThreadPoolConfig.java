package com.pitaya.devdiscovery.aiability.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class AIThreadPoolConfig {
    private static final Logger logger = LoggerFactory.getLogger(AIThreadPoolConfig.class);
    public static final String TASK_EXECUTOR_NAME = "aiAsyncTaskExecutor";
    public static final String TASK_EXECUTOR_BUS_NAME = "aiBusAsyncTaskBusExecutor";
    public static final String BUSINESS_RECOMMEND_TASK_EXECUTOR_NAME = "aiBusinessRecommendTaskExecutor";


    @<PERSON>(name = TASK_EXECUTOR_NAME)
    public Executor aiAsyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int corePoolSize = 5;
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(corePoolSize);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setThreadNamePrefix("ai-async-task-");
        executor.setKeepAliveSeconds(300);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(600);
        executor.setThreadFactory(r -> {
            Thread thread = new Thread(r);
            thread.setUncaughtExceptionHandler((t, e) -> {
                logger.error("线程池任务执行异常: {}", e.getMessage(), e);
            });
            return thread;
        });
        executor.initialize();
        return executor;
    }

    @Bean(name = TASK_EXECUTOR_BUS_NAME)
    public Executor aiAsyncBusTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int corePoolSize = 5;
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(corePoolSize);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setThreadNamePrefix("ai-async-task-");
        executor.setKeepAliveSeconds(300);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(600);
        executor.setThreadFactory(r -> {
            Thread thread = new Thread(r);
            thread.setUncaughtExceptionHandler((t, e) -> {
                logger.error("线程池任务执行异常: {}", e.getMessage(), e);
            });
            return thread;
        });
        executor.initialize();
        return executor;
    }
    @Bean(name = BUSINESS_RECOMMEND_TASK_EXECUTOR_NAME)
    public Executor aiBusinessRecommendTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int corePoolSize = 3;
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(corePoolSize);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setThreadNamePrefix("ai-async-task-");
        executor.setKeepAliveSeconds(300);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(600);
        executor.setThreadFactory(r -> {
            Thread thread = new Thread(r);
            thread.setUncaughtExceptionHandler((t, e) -> {
                logger.error("线程池任务执行异常: {}", e.getMessage(), e);
            });
            return thread;
        });
        executor.initialize();
        return executor;
    }
}

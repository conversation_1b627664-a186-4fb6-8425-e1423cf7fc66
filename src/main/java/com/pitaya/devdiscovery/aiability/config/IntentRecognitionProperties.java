package com.pitaya.devdiscovery.aiability.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @program: dev-discovery
 * @ClassName IntentRecognitionProperties
 * @description: 意图识别API配置属性
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Data
@Component
@ConfigurationProperties(prefix = "api.intent-recognition")
public class IntentRecognitionProperties {

    /**
     * API基础URL
     */
    private String baseUrl = "http://*************:8080";

    /**
     * 意图识别接口路径
     */
    private String intentRecognitionPath = "/sdai/activity/requestforwarding/qa_pair/intent_recognition";

    /**
     * AppKey
     */
    private String appKey = "pRFsfsRN";

    /**
     * AppSecret
     */
    private String appSecret = "54b6299a79d7e1601e533bf53091ae188ed0f96f";

    /**
     * 获取完整的意图识别接口URL
     *
     * @return 完整URL
     */
    public String getIntentRecognitionUrl() {
        return baseUrl + intentRecognitionPath;
    }
}
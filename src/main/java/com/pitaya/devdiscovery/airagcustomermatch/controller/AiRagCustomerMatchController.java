package com.pitaya.devdiscovery.airagcustomermatch.controller;

import com.pitaya.devdiscovery.airagcustomermatch.entity.CustomerMatchTask;
import com.pitaya.devdiscovery.airagcustomermatch.service.CustomerMatchTaskService;
import com.pitaya.devdiscovery.aiability.dto.BatchQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @program: dev-discovery
 * @ClassName AiRagCustomerMatchController
 * @description: AI RAG客户匹配控制器
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/airag/customer-match")
public class AiRagCustomerMatchController {

    @Autowired
    private CustomerMatchTaskService customerMatchTaskService;

    /**
     * 批量客户匹配功能（从原接口迁移）
     *
     * @param request 包含逗号分隔查询字符串的对象 (e.g., {"queries": "自然资源,国土,物流"})
     * @return 批量匹配结果
     */
    @PostMapping("/customers/batch")
    public ResponseEntity<Map<String, Object>> batchMatchCustomers(@RequestBody BatchQueryRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("批量客户匹配接口被调用, 查询参数: {}", request.getQueries());

            // 参数验证
            if (!StringUtils.hasText(request.getQueries())) {
                response.put("success", false);
                response.put("message", "查询参数 'queries' 不能为空");
                response.put("totalQueries", 0);
                response.put("results", Collections.emptyList());
                return ResponseEntity.badRequest().body(response);
            }

            // 调用服务处理批量匹配
            List<Object> results = customerMatchTaskService.batchMatchCustomers(
                    request.getQueries(),
                    request.getIncludeDetails());

            // 统计结果
            int successCount = 0;
            int failCount = 0;
            for (Object result : results) {
                if (result instanceof Map) {
                    Map<String, Object> resultMap = (Map<String, Object>) result;
                    if (Boolean.TRUE.equals(resultMap.get("success"))) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                }
            }

            // 构建响应
            response.put("success", true);
            response.put("message", String.format("批量查询完成，成功 %d 个，失败 %d 个", successCount, failCount));
            response.put("totalQueries", results.size());
            response.put("successCount", successCount);
            response.put("failCount", failCount);
            response.put("results", results);

            log.info("批量匹配完成，总查询数: {}, 成功: {}, 失败: {}", results.size(), successCount, failCount);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("批量匹配处理异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "批量处理时发生错误: " + e.getMessage());
            response.put("totalQueries", 0);
            response.put("results", Collections.emptyList());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 扫描并处理等待处理的批量任务
     *
     * @param limit 一次处理的批量任务数量限制（可选，默认10）
     * @return 处理结果
     */
    @PostMapping("/batch-tasks/scan-process")
    public ResponseEntity<Map<String, Object>> scanAndProcessBatchTasks(@RequestParam(defaultValue = "10") Integer limit) {
        Map<String, Object> response = new HashMap<>();
        try {
            log.info("扫描处理批量任务接口被调用, 限制数量: {}", limit);
            // 参数验证
            if (limit <= 0 || limit > 100) {
                response.put("success", false);
                response.put("message", "limit参数必须在1-100之间");
                response.put("processedCount", 0);
                return ResponseEntity.badRequest().body(response);
            }
            // 扫描并处理等待的批量任务
            int processedCount = customerMatchTaskService.scanAndProcessWaitingBatchTasks(limit);
            response.put("success", true);
            response.put("message", String.format("成功扫描到 %d 个等待处理的批量任务，已开始异步处理", processedCount));
            response.put("processedCount", processedCount);
            response.put("limit", limit);
            log.info("批量任务扫描完成，处理任务数: {}", processedCount);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("扫描处理批量任务异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "扫描处理批量任务时发生错误: " + e.getMessage());
            response.put("processedCount", 0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 扫描并处理等待处理的任务
     *
     * @param limit 一次处理的任务数量限制（可选，默认10）
     * @return 处理结果
     */
    @PostMapping("/tasks/scan-process")
    public ResponseEntity<Map<String, Object>> scanAndProcessTasks(
            @RequestParam(defaultValue = "10") Integer limit) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("扫描处理任务接口被调用, 限制数量: {}", limit);

            // 参数验证
            if (limit <= 0 || limit > 100) {
                response.put("success", false);
                response.put("message", "limit参数必须在1-100之间");
                response.put("processedCount", 0);
                return ResponseEntity.badRequest().body(response);
            }

            // 扫描并处理等待任务
            int processedCount = customerMatchTaskService.scanAndProcessWaitingTasks(limit);

            response.put("success", true);
            response.put("message", String.format("成功扫描到 %d 个等待处理的任务，已开始异步处理", processedCount));
            response.put("processedCount", processedCount);
            response.put("limit", limit);

            log.info("任务扫描完成，处理任务数: {}", processedCount);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("扫描处理任务异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "扫描处理任务时发生错误: " + e.getMessage());
            response.put("processedCount", 0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 创建新的客户匹配任务
     *
     * @param requestBody 包含查询内容的请求体
     * @return 创建结果
     */
    @PostMapping("/tasks/create")
    public ResponseEntity<Map<String, Object>> createTask(@RequestBody Map<String, String> requestBody) {
        Map<String, Object> response = new HashMap<>();

        try {
            String query = requestBody.get("query");
            log.info("创建任务接口被调用, 查询内容: {}", query);

            // 参数验证
            if (!StringUtils.hasText(query)) {
                response.put("success", false);
                response.put("message", "查询内容 'query' 不能为空");
                response.put("taskId", null);
                return ResponseEntity.badRequest().body(response);
            }

            // 创建任务
            Long taskId = customerMatchTaskService.createTask(query);

            response.put("success", true);
            response.put("message", "任务创建成功");
            response.put("taskId", taskId);
            response.put("query", query);

            log.info("任务创建成功，任务ID: {}", taskId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("创建任务异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "创建任务时发生错误: " + e.getMessage());
            response.put("taskId", null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 创建批量任务（指定任务ID）
     *
     * @param requestBody 包含任务ID和查询内容的请求体
     * @return 创建结果
     */
    @PostMapping("/batch-tasks/create")
    public ResponseEntity<Map<String, Object>> createBatchTask(@RequestBody Map<String, Object> requestBody) {
        Map<String, Object> response = new HashMap<>();

        try {
            String taskId = (String) requestBody.get("taskId");
            String queries = (String) requestBody.get("queries");

            log.info("创建批量任务接口被调用, 任务ID: {}, 查询参数: {}", taskId, queries);

            // 参数验证
            if (!StringUtils.hasText(taskId)) {
                response.put("success", false);
                response.put("message", "任务ID 'taskId' 不能为空");
                response.put("createdCount", 0);
                return ResponseEntity.badRequest().body(response);
            }

            if (!StringUtils.hasText(queries)) {
                response.put("success", false);
                response.put("message", "查询参数 'queries' 不能为空");
                response.put("createdCount", 0);
                return ResponseEntity.badRequest().body(response);
            }

            // 解析查询内容
            String[] queryArray = queries.split(",");
            List<String> validQueries = new ArrayList<>();

            for (String query : queryArray) {
                String trimmedQuery = query.trim();
                if (StringUtils.hasText(trimmedQuery)) {
                    validQueries.add(trimmedQuery);
                }
            }

            if (validQueries.isEmpty()) {
                response.put("success", false);
                response.put("message", "没有有效的查询内容");
                response.put("createdCount", 0);
                return ResponseEntity.badRequest().body(response);
            }

            // 创建批量任务
            int createdCount = customerMatchTaskService.createBatchTask(taskId, validQueries);

            response.put("success", true);
            response.put("message", String.format("成功创建批量任务，包含 %d 条记录", createdCount));
            response.put("taskId", taskId);
            response.put("createdCount", createdCount);
            response.put("totalQueries", validQueries.size());

            log.info("批量任务创建完成，任务ID: {}, 成功创建 {} 条记录", taskId, createdCount);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("创建批量任务异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "创建批量任务时发生错误: " + e.getMessage());
            response.put("createdCount", 0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量创建客户匹配任务（自动生成任务ID）
     *
     * @param request 包含查询内容的请求对象
     * @return 创建结果
     */
    @PostMapping("/tasks/batch-create")
    public ResponseEntity<Map<String, Object>> batchCreateTasks(@RequestBody BatchQueryRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("批量创建任务接口被调用, 查询参数: {}", request.getQueries());

            // 参数验证
            if (!StringUtils.hasText(request.getQueries())) {
                response.put("success", false);
                response.put("message", "查询参数 'queries' 不能为空");
                response.put("taskId", null);
                return ResponseEntity.badRequest().body(response);
            }

            // 解析查询内容
            String[] queryArray = request.getQueries().split(",");
            List<String> validQueries = new ArrayList<>();

            for (String query : queryArray) {
                String trimmedQuery = query.trim();
                if (StringUtils.hasText(trimmedQuery)) {
                    validQueries.add(trimmedQuery);
                }
            }

            if (validQueries.isEmpty()) {
                response.put("success", false);
                response.put("message", "没有有效的查询内容");
                response.put("taskId", null);
                return ResponseEntity.badRequest().body(response);
            }

            // 批量创建任务
            String taskId = customerMatchTaskService.batchCreateTasks(validQueries);

            if (taskId != null) {
                response.put("success", true);
                response.put("message", String.format("成功创建批量任务，包含 %d 条记录", validQueries.size()));
                response.put("taskId", taskId);
                response.put("totalQueries", validQueries.size());
            } else {
                response.put("success", false);
                response.put("message", "创建批量任务失败");
                response.put("taskId", null);
            }

            log.info("批量创建任务完成，任务ID: {}", taskId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("批量创建任务异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "批量创建任务时发生错误: " + e.getMessage());
            response.put("taskId", null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 查询批量任务状态
     *
     * @param taskId 任务ID
     * @return 批量任务信息
     */
    @GetMapping("/batch-tasks/{taskId}")
    public ResponseEntity<Map<String, Object>> getBatchTaskStatus(@PathVariable String taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("查询批量任务状态接口被调用, 任务ID: {}", taskId);

            List<CustomerMatchTask> tasks = customerMatchTaskService.getTasksByTaskId(taskId);

            if (tasks.isEmpty()) {
                response.put("success", false);
                response.put("message", "批量任务不存在");
                response.put("tasks", Collections.emptyList());
                response.put("totalCount", 0);
                return ResponseEntity.notFound().build();
            }

            // 统计各状态的数量
            long waitingCount = tasks.stream().filter(t -> CustomerMatchTask.STATUS_WAITING.equals(t.getStatus()))
                    .count();
            long processingCount = tasks.stream().filter(t -> CustomerMatchTask.STATUS_PROCESSING.equals(t.getStatus()))
                    .count();
            long completedCount = tasks.stream().filter(t -> CustomerMatchTask.STATUS_COMPLETED.equals(t.getStatus()))
                    .count();

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("taskId", taskId);
            response.put("tasks", tasks);
            response.put("totalCount", tasks.size());
            response.put("waitingCount", waitingCount);
            response.put("processingCount", processingCount);
            response.put("completedCount", completedCount);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("查询批量任务状态异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询批量任务状态时发生错误: " + e.getMessage());
            response.put("tasks", Collections.emptyList());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 手动触发批量任务的异步处理
     *
     * @param taskId 任务ID
     * @return 处理结果
     */
    @PostMapping("/batch-tasks/{taskId}/process")
    public ResponseEntity<Map<String, Object>> processBatchTask(@PathVariable String taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("手动处理批量任务接口被调用, 任务ID: {}", taskId);

            // 检查批量任务是否存在
            List<CustomerMatchTask> tasks = customerMatchTaskService.getTasksByTaskId(taskId);
            if (tasks.isEmpty()) {
                response.put("success", false);
                response.put("message", "批量任务不存在");
                return ResponseEntity.notFound().build();
            }

            // 异步处理批量任务
            customerMatchTaskService.processBatchTaskAsync(taskId);

            response.put("success", true);
            response.put("message", "批量任务已开始异步处理");
            response.put("taskId", taskId);
            response.put("totalTasks", tasks.size());

            log.info("批量任务开始处理，任务ID: {}, 包含 {} 条记录", taskId, tasks.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("手动处理批量任务异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "处理批量任务时发生错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 查询任务状态
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    @GetMapping("/tasks/{taskId}")
    public ResponseEntity<Map<String, Object>> getTaskStatus(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("查询任务状态接口被调用, 任务ID: {}", taskId);

            CustomerMatchTask task = customerMatchTaskService.getTaskById(taskId);

            if (task == null) {
                response.put("success", false);
                response.put("message", "任务不存在");
                response.put("task", null);
                return ResponseEntity.notFound().build();
            }

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("task", task);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("查询任务状态异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询任务状态时发生错误: " + e.getMessage());
            response.put("task", null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 手动触发单个任务的异步处理
     *
     * @param taskId 任务ID
     * @return 处理结果
     */
    @PostMapping("/tasks/{taskId}/process")
    public ResponseEntity<Map<String, Object>> processTask(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("手动处理任务接口被调用, 任务ID: {}", taskId);

            // 检查任务是否存在
            CustomerMatchTask task = customerMatchTaskService.getTaskById(taskId);
            if (task == null) {
                response.put("success", false);
                response.put("message", "任务不存在");
                return ResponseEntity.notFound().build();
            }

            // 异步处理任务
            customerMatchTaskService.processTaskAsync(taskId);

            response.put("success", true);
            response.put("message", "任务已开始异步处理");
            response.put("taskId", taskId);

            log.info("任务开始处理，任务ID: {}", taskId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("手动处理任务异常: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "处理任务时发生错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}

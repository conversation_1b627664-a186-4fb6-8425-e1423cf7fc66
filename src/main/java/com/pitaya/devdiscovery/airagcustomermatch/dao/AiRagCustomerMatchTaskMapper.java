package com.pitaya.devdiscovery.airagcustomermatch.dao;

import com.pitaya.devdiscovery.airagcustomermatch.entity.CustomerMatchTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName AiRagCustomerMatchTaskMapper
 * @description: AI RAG客户匹配任务Mapper接口
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Mapper
public interface AiRagCustomerMatchTaskMapper {

    /**
     * 查询等待处理的任务（按任务ID分组）
     *
     * @param limit 限制条数
     * @return 任务ID列表
     */
    List<String> selectWaitingTaskIds(@Param("limit") Integer limit);

    /**
     * 根据任务ID查询所有相关记录
     *
     * @param taskId 任务ID
     * @return 任务记录列表
     */
    List<CustomerMatchTask> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 更新任务状态（根据任务ID更新所有相关记录）
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @return 更新条数
     */
    int updateTaskStatusByTaskId(@Param("taskId") String taskId, @Param("status") String status);

    /**
     * 更新单条记录的状态
     *
     * @param id     记录主键ID
     * @param status 新状态
     * @return 更新条数
     */
    int updateTaskStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 插入新任务
     *
     * @param task 任务对象
     * @return 插入条数
     */
    int insertTask(CustomerMatchTask task);

    /**
     * 根据主键ID查询任务
     *
     * @param id 主键ID
     * @return 任务对象
     */
    CustomerMatchTask selectById(@Param("id") Long id);

    /**
     * 批量插入任务
     *
     * @param tasks 任务列表
     * @return 插入条数
     */
    int batchInsertTasks(@Param("tasks") List<CustomerMatchTask> tasks);

    /**
     * 查询等待处理的任务记录
     *
     * @param limit 限制条数
     * @return 任务列表
     */
    List<CustomerMatchTask> selectWaitingTasks(@Param("limit") Integer limit);
}
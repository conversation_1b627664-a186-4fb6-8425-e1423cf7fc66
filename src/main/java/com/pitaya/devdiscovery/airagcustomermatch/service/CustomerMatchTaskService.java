package com.pitaya.devdiscovery.airagcustomermatch.service;

import com.pitaya.devdiscovery.airagcustomermatch.entity.CustomerMatchTask;
import com.pitaya.devdiscovery.aiability.dto.ragcustomermatch.IntentData;

import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName CustomerMatchTaskService
 * @description: 客户匹配任务服务接口
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
public interface CustomerMatchTaskService {

    /**
     * 批量客户匹配（同步）
     *
     * @param queries        查询内容，逗号分隔
     * @param includeDetails 是否返回详细数据
     * @return 匹配结果
     */
    List<Object> batchMatchCustomers(String queries, Boolean includeDetails);

    /**
     * 扫描并处理等待处理的批量任务
     *
     * @param limit 一次处理的批量任务数量限制
     * @return 处理的批量任务数量
     */
    int scanAndProcessWaitingBatchTasks(Integer limit);

    /**
     * 异步处理批量任务（根据任务ID）
     *
     * @param taskId 任务ID
     */
    void processBatchTaskAsync(String taskId);

    /**
     * 创建新的批量任务
     *
     * @param taskId  任务ID
     * @param queries 查询内容列表
     * @return 创建的记录数量
     */
    int createBatchTask(String taskId, List<String> queries);

    /**
     * 批量创建任务（生成随机任务ID）
     *
     * @param queries 查询内容列表
     * @return 任务ID
     */
    String batchCreateTasks(List<String> queries);

    /**
     * 更新批量任务状态（根据任务ID）
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateBatchTaskStatus(String taskId, String status);

    /**
     * 根据任务ID获取所有相关记录
     *
     * @param taskId 任务ID
     * @return 任务记录列表
     */
    List<CustomerMatchTask> getTasksByTaskId(String taskId);

    /**
     * 扫描并处理等待处理的任务
     *
     * @param limit 一次处理的任务数量限制
     * @return 处理的任务数量
     */
    int scanAndProcessWaitingTasks(Integer limit);

    /**
     * 异步处理单个任务
     *
     * @param taskId 任务ID
     */
    void processTaskAsync(Long taskId);

    /**
     * 创建新任务
     *
     * @param query 查询内容
     * @return 任务ID
     */
    Long createTask(String query);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateTaskStatus(Long taskId, String status);

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务对象
     */
    CustomerMatchTask getTaskById(Long taskId);

    /**
     * 处理单个客户匹配任务
     *
     * @param query 查询内容
     * @return 匹配结果
     */
    List<IntentData> matchCustomers(String query);
}
package com.pitaya.devdiscovery.airagcustomermatch.service.impl;

import com.pitaya.devdiscovery.airagcustomermatch.dao.AiRagCustomerMatchTaskMapper;
import com.pitaya.devdiscovery.airagcustomermatch.entity.CustomerMatchTask;
import com.pitaya.devdiscovery.airagcustomermatch.service.CustomerMatchTaskService;
import com.pitaya.devdiscovery.aiability.dto.ragcustomermatch.IntentData;
import com.pitaya.devdiscovery.aiability.service.RagCustomerMatchService;
import com.pitaya.devdiscovery.aiability.service.CustomerMatchLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.UUID;

/**
 * @program: dev-discovery
 * @ClassName CustomerMatchTaskServiceImpl
 * @description: 客户匹配任务服务实现类
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Slf4j
@Service
public class CustomerMatchTaskServiceImpl implements CustomerMatchTaskService {

    @Autowired
    private AiRagCustomerMatchTaskMapper aiRagCustomerMatchTaskMapper;

    @Autowired
    private RagCustomerMatchService ragCustomerMatchService;

    @Autowired
    private CustomerMatchLogService customerMatchLogService;

    @Override
    public List<Object> batchMatchCustomers(String queries, Boolean includeDetails) {
        log.info("开始批量客户匹配，查询参数: {}", queries);

        // 参数验证
        if (!StringUtils.hasText(queries)) {
            log.warn("查询参数为空");
            return Collections.emptyList();
        }

        // 解析查询内容
        String[] queryArray = queries.split(",");
        List<String> validQueries = new ArrayList<>();

        // 过滤和清理查询内容
        for (String query : queryArray) {
            String trimmedQuery = query.trim();
            if (StringUtils.hasText(trimmedQuery)) {
                validQueries.add(trimmedQuery);
            }
        }

        if (validQueries.isEmpty()) {
            log.warn("没有有效的查询内容");
            return Collections.emptyList();
        }

        log.info("解析到 {} 个有效查询: {}", validQueries.size(), validQueries);

        // 批量处理
        List<Object> results = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        for (String query : validQueries) {
            Map<String, Object> queryResult = new HashMap<>();
            queryResult.put("query", query);

            try {
                // 调用匹配服务
                List<IntentData> matchedCustomers = ragCustomerMatchService.matchCustomers(query);

                // 保存到数据库
                String requestId = null;
                try {
                    requestId = customerMatchLogService.saveMatchResults(query, matchedCustomers);
                    log.info("查询 '{}' 成功保存到数据库，请求ID: {}", query, requestId);
                } catch (Exception e) {
                    log.error("查询 '{}' 保存到数据库失败: {}", query, e.getMessage());
                    // 继续处理，不因保存失败而中断
                }

                queryResult.put("success", true);
                queryResult.put("count", matchedCustomers.size());
                queryResult.put("requestId", requestId);

                // 根据includeDetails决定是否返回详细数据
                if (Boolean.TRUE.equals(includeDetails)) {
                    queryResult.put("data", matchedCustomers);
                }

                successCount++;
                log.info("查询 '{}' 成功，匹配到 {} 条结果", query, matchedCustomers.size());

            } catch (Exception e) {
                log.error("查询 '{}' 失败: {}", query, e.getMessage(), e);
                queryResult.put("success", false);
                queryResult.put("count", 0);
                queryResult.put("error", e.getMessage());
                queryResult.put("requestId", null);
                failCount++;
            }

            results.add(queryResult);
        }

        log.info("批量匹配完成，总查询数: {}, 成功: {}, 失败: {}", validQueries.size(), successCount, failCount);
        return results;
    }

    @Override
    public int scanAndProcessWaitingBatchTasks(Integer limit) {
        log.info("开始扫描等待处理的批量任务，限制数量: {}", limit);

        try {
            // 查询等待处理的任务ID（去重）
            List<String> waitingTaskIds = aiRagCustomerMatchTaskMapper.selectWaitingTaskIds(limit);

            if (waitingTaskIds.isEmpty()) {
                log.info("没有等待处理的批量任务");
                return 0;
            }

            log.info("找到 {} 个等待处理的批量任务", waitingTaskIds.size());

            // 异步处理每个批量任务
            for (String taskId : waitingTaskIds) {
                processBatchTaskAsync(taskId);
            }

            return waitingTaskIds.size();

        } catch (Exception e) {
            log.error("扫描处理等待批量任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("扫描处理等待批量任务失败: " + e.getMessage(), e);
        }
    }

    @Async
    @Override
    public void processBatchTaskAsync(String taskId) {
        log.info("开始异步处理批量任务，任务ID: {}", taskId);

        try {
            // 获取该任务ID下的所有记录
            List<CustomerMatchTask> tasks = aiRagCustomerMatchTaskMapper.selectByTaskId(taskId);
            if (tasks.isEmpty()) {
                log.warn("任务不存在或已被处理，任务ID: {}", taskId);
                return;
            }

            // 更新任务状态为正在处理
            aiRagCustomerMatchTaskMapper.updateTaskStatusByTaskId(taskId, CustomerMatchTask.STATUS_PROCESSING);
            log.info("批量任务状态已更新为正在处理，任务ID: {}, 记录数: {}", taskId, tasks.size());

            // 循环处理每个查询
            int successCount = 0;
            int failCount = 0;
            for (CustomerMatchTask task : tasks) {
                try {
                    // 执行客户匹配
                    List<IntentData> matchResults = ragCustomerMatchService.matchCustomers(task.getQuery());

                    // 保存匹配结果到日志表
                    String requestId = customerMatchLogService.saveMatchResults(task.getQuery(), matchResults);
                    log.info("查询 '{}' 处理完成，匹配到 {} 条结果，请求ID: {}",
                            task.getQuery(), matchResults.size(), requestId);
                    successCount++;

                } catch (Exception e) {
                    log.error("处理查询 '{}' 失败: {}", task.getQuery(), e.getMessage(), e);
                    failCount++;
                }
            }

            // 更新任务状态为处理完成
            aiRagCustomerMatchTaskMapper.updateTaskStatusByTaskId(taskId, CustomerMatchTask.STATUS_COMPLETED);
            log.info("批量任务处理完成，任务ID: {}, 成功: {}, 失败: {}", taskId, successCount, failCount);

        } catch (Exception e) {
            log.error("异步处理批量任务失败，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);

            // 处理失败时，将任务状态重置为等待处理
            try {
                aiRagCustomerMatchTaskMapper.updateTaskStatusByTaskId(taskId, CustomerMatchTask.STATUS_WAITING);
                log.info("批量任务处理失败，状态已重置为等待处理，任务ID: {}", taskId);
            } catch (Exception updateException) {
                log.error("更新批量任务状态失败，任务ID: {}, 错误: {}", taskId, updateException.getMessage());
            }
        }
    }

    @Override
    public int scanAndProcessWaitingTasks(Integer limit) {
        log.info("开始扫描等待处理的任务，限制数量: {}", limit);

        try {
            // 查询等待处理的任务
            List<CustomerMatchTask> waitingTasks = aiRagCustomerMatchTaskMapper.selectWaitingTasks(limit);

            if (waitingTasks.isEmpty()) {
                log.info("没有等待处理的任务");
                return 0;
            }

            log.info("找到 {} 个等待处理的任务", waitingTasks.size());

            // 异步处理每个任务
            for (CustomerMatchTask task : waitingTasks) {
                processTaskAsync(task.getId());
            }

            return waitingTasks.size();

        } catch (Exception e) {
            log.error("扫描处理等待任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("扫描处理等待任务失败: " + e.getMessage(), e);
        }
    }

    @Async
    @Override
    public void processTaskAsync(Long taskId) {
        log.info("开始异步处理任务，任务ID: {}", taskId);

        try {
            // 获取任务信息
            CustomerMatchTask task = aiRagCustomerMatchTaskMapper.selectById(taskId);
            if (task == null) {
                log.warn("任务不存在，任务ID: {}", taskId);
                return;
            }

            // 更新任务状态为正在处理
            aiRagCustomerMatchTaskMapper.updateTaskStatus(taskId, CustomerMatchTask.STATUS_PROCESSING);
            log.info("任务状态已更新为正在处理，任务ID: {}", taskId);

            // 执行客户匹配
            List<IntentData> matchResults = ragCustomerMatchService.matchCustomers(task.getQuery());

            // 保存匹配结果到日志表
            String requestId = customerMatchLogService.saveMatchResults(task.getQuery(), matchResults);
            log.info("任务处理完成，匹配到 {} 条结果，请求ID: {}", matchResults.size(), requestId);

            // 更新任务状态为处理完成
            aiRagCustomerMatchTaskMapper.updateTaskStatus(taskId, CustomerMatchTask.STATUS_COMPLETED);
            log.info("任务处理完成，任务ID: {}", taskId);

        } catch (Exception e) {
            log.error("异步处理任务失败，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);

            // 处理失败时，可以考虑将任务状态重置为等待处理，或者设置为失败状态
            try {
                aiRagCustomerMatchTaskMapper.updateTaskStatus(taskId, CustomerMatchTask.STATUS_WAITING);
                log.info("任务处理失败，状态已重置为等待处理，任务ID: {}", taskId);
            } catch (Exception updateException) {
                log.error("更新任务状态失败，任务ID: {}, 错误: {}", taskId, updateException.getMessage());
            }
        }
    }

    @Override
    public int createBatchTask(String taskId, List<String> queries) {
        log.info("创建批量任务，任务ID: {}, 查询数量: {}", taskId, queries.size());

        try {
            List<CustomerMatchTask> tasks = new ArrayList<>();
            for (String query : queries) {
                if (StringUtils.hasText(query.trim())) {
                    CustomerMatchTask task = new CustomerMatchTask();
                    task.setTaskId(taskId);
                    task.setQuery(query.trim());
                    task.setStatus(CustomerMatchTask.STATUS_WAITING);
                    tasks.add(task);
                }
            }

            if (tasks.isEmpty()) {
                log.warn("没有有效的查询内容，无法创建批量任务");
                return 0;
            }

            int insertCount = aiRagCustomerMatchTaskMapper.batchInsertTasks(tasks);
            log.info("批量任务创建完成，任务ID: {}, 成功创建 {} 条记录", taskId, insertCount);

            return insertCount;

        } catch (Exception e) {
            log.error("创建批量任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建批量任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String batchCreateTasks(List<String> queries) {
        log.info("批量创建任务（自动生成任务ID），查询数量: {}", queries.size());

        try {
            // 生成唯一的任务ID
            String taskId = generateTaskId();

            // 创建批量任务
            int insertCount = createBatchTask(taskId, queries);

            if (insertCount > 0) {
                log.info("批量任务创建成功，任务ID: {}, 创建记录数: {}", taskId, insertCount);
                return taskId;
            } else {
                log.warn("批量任务创建失败，没有有效的查询内容");
                return null;
            }

        } catch (Exception e) {
            log.error("批量创建任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量创建任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateBatchTaskStatus(String taskId, String status) {
        try {
            int updateCount = aiRagCustomerMatchTaskMapper.updateTaskStatusByTaskId(taskId, status);
            log.info("批量任务状态更新，任务ID: {}, 状态: {}, 更新记录数: {}", taskId, status, updateCount);
            return updateCount > 0;
        } catch (Exception e) {
            log.error("更新批量任务状态失败，任务ID: {}, 状态: {}, 错误: {}", taskId, status, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<CustomerMatchTask> getTasksByTaskId(String taskId) {
        try {
            return aiRagCustomerMatchTaskMapper.selectByTaskId(taskId);
        } catch (Exception e) {
            log.error("查询批量任务失败，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public Long createTask(String query) {
        log.info("创建新任务，查询内容: {}", query);

        try {
            CustomerMatchTask task = new CustomerMatchTask();
            task.setTaskId(generateTaskId()); // 为单个任务也生成任务ID
            task.setQuery(query);
            task.setStatus(CustomerMatchTask.STATUS_WAITING);

            aiRagCustomerMatchTaskMapper.insertTask(task);
            log.info("任务创建成功，主键ID: {}, 任务ID: {}", task.getId(), task.getTaskId());

            return task.getId();

        } catch (Exception e) {
            log.error("创建任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateTaskStatus(Long taskId, String status) {
        try {
            int updateCount = aiRagCustomerMatchTaskMapper.updateTaskStatus(taskId, status);
            return updateCount > 0;
        } catch (Exception e) {
            log.error("更新任务状态失败，任务ID: {}, 状态: {}, 错误: {}", taskId, status, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public CustomerMatchTask getTaskById(Long taskId) {
        try {
            return aiRagCustomerMatchTaskMapper.selectById(taskId);
        } catch (Exception e) {
            log.error("查询任务失败，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<IntentData> matchCustomers(String query) {
        return ragCustomerMatchService.matchCustomers(query);
    }

    /**
     * 生成唯一的任务ID
     * 格式: TASK_yyyyMMdd_HHmmss_UUID前8位
     */
    private String generateTaskId() {
        String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "TASK_" + timestamp + "_" + uuid;
    }
}
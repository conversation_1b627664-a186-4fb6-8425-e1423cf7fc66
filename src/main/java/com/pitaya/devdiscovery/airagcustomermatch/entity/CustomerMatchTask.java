package com.pitaya.devdiscovery.airagcustomermatch.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: dev-discovery
 * @ClassName CustomerMatchTask
 * @description: 客户匹配任务实体类
 * @author: AI Assistant
 * @date: 2025-01-xx xx:xx
 * @Version 1.0
 **/
@Data
public class CustomerMatchTask {

    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 任务ID（同一批任务使用相同的任务ID）
     */
    private String taskId;

    /**
     * 查询内容（如公司名称）
     */
    private String query;

    /**
     * 状态：0-等待处理，1-正在处理，2-处理完成
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态常量
     */
    public static final String STATUS_WAITING = "0";
    public static final String STATUS_PROCESSING = "1";
    public static final String STATUS_COMPLETED = "2";
}
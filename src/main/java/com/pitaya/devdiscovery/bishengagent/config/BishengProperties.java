package com.pitaya.devdiscovery.bishengagent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 毕昇智能体API配置属性
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bisheng")
public class BishengProperties {
    /**
     * 毕昇API的基础URL
     */
    private String baseUrl = "https://bisheng.dataelem.com";

    /**
     * 工作流调用API的路径
     */
    private String invokeApi = "/api/v2/workflow/invoke";

    /**
     * 工作流停止API的路径
     */
    private String stopApi = "/api/v2/workflow/stop";

    /**
     * API请求超时时间(秒)
     */
    private Integer timeout = 60;

    /**
     * 获取完整的工作流调用API URL
     */
    public String getInvokeUrl(String flowId) {
        return baseUrl +"/api/v2/workflow/"+flowId +"/invoke";
    }

    /**
     * 获取完整的工作流停止API URL
     */
    public String getStopUrl() {
        return baseUrl + stopApi;
    }
}

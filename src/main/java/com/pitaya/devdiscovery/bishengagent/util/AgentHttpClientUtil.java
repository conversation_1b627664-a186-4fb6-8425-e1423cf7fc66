package com.pitaya.devdiscovery.bishengagent.util;

import com.alibaba.fastjson2.JSONObject;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

/**
 * HTTP客户端工具类
 */
@Component
public class AgentHttpClientUtil {
    private static final Logger logger = LoggerFactory.getLogger(AgentHttpClientUtil.class);
    private static final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(300, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();
    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    public String post(String url, String jsonBody) throws IOException {
        RequestBody body = RequestBody.create(jsonBody, JSON_TYPE);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            ResponseBody responseBody = response.body();
            return responseBody.string();
        } catch (IOException e) {
            throw e;
        }
    }

    public String postStream(String url, String jsonBody) throws IOException {
        RequestBody body = RequestBody.create(jsonBody, JSON_TYPE);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        JSONObject result = new JSONObject();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return "Error";
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("line: {}", line);
                    if (line.startsWith("data: ")) {
                        String respStr = line.substring(5).trim();
                        JSONObject respJSON = JSONObject.parseObject(respStr);
                        JSONObject respData = respJSON.getJSONObject("data");
                        if ("output_msg".equals(respData.getString("event"))) {
                            String nodeId = respData.getString("node_id");
                            JSONObject outputSchema = respData.getJSONObject("output_schema");
                            String messageContent = outputSchema.getString("message");
                            result.put(nodeId, messageContent);

                        }
                    }
                }
            }
        } catch (IOException e) {
            return "Error";
        }
        return JSONObject.toJSONString(result);
    }
}

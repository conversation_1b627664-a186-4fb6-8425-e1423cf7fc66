package com.pitaya.devdiscovery.bishengagent.util;

/**
 * 毕昇API调用异常
 */
public class BishengException extends RuntimeException {

    private Integer code;

    public BishengException(String message) {
        super(message);
        this.code = 500;
    }

    public BishengException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public BishengException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
    }

    public BishengException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }
}
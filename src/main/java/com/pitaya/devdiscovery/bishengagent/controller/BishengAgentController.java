package com.pitaya.devdiscovery.bishengagent.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.bishengagent.service.BishengAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 毕昇智能体API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/bisheng")
public class BishengAgentController {

    @Autowired
    private BishengAgentService bishengAgentService;

    /**
     * 调用毕昇智能体工作流
     * @param requestParams 请求参数，需要包含flowId和inputMap
     * @return 工作流输出结果
     */
    @PostMapping("/invoke")
    public ResponseEntity<Map<String, Object>> invokeWorkflow(@RequestBody JSONObject requestParams) {
        log.info("接收到工作流调用请求: {}", requestParams);
        String flowId = requestParams.getString("flowId");
        JSONObject inputJSON = requestParams.getJSONObject("inputMap");
        Map<String, Object> result = bishengAgentService.invokeWorkflow(flowId, inputJSON);
        String contentStr = (String)result.get("content");
        JSONObject content = JSONObject.parseObject(contentStr);
        Set<String> keys = content.keySet();
        for (String key : keys) {
            Object value = content.get(key);
            if (value instanceof String) {
                String strValue = (String) value;
                JSONObject jsonValue = JSON.parseObject(strValue);
                System.out.println(JSONObject.toJSONString(jsonValue));
            }
        }
        return ResponseEntity.ok(result);
    }
}

package com.pitaya.devdiscovery.bishengagent.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.bishengagent.config.BishengProperties;
import com.pitaya.devdiscovery.bishengagent.dto.BishengResponse;
import com.pitaya.devdiscovery.bishengagent.service.BishengAgentService;
import com.pitaya.devdiscovery.bishengagent.util.BishengException;
import com.pitaya.devdiscovery.bishengagent.util.AgentHttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 毕昇智能体服务实现
 */
@Slf4j
@Service
public class BishengAgentServiceImpl implements BishengAgentService {
    @Autowired
    private AgentHttpClientUtil agentHttpClientUtil;
    @Autowired
    private BishengProperties bishengProperties;
    /**
     * 调用毕昇智能体工作流
     *
     * @param flowId    工作流ID
     * @param inputJSON 用户输入参数
     * @return 工作流输出结果
     */
    @Override
    public Map<String, Object> invokeWorkflow(String flowId, JSONObject inputJSON) {
        if (!StringUtils.hasText(flowId)) {
            throw new BishengException("工作流ID不能为空");
        }
        log.info("调用智能体工作流开始，flowId: {}, 输入参数: {}", flowId, JSON.toJSONString(inputJSON));
        try {
            Map<String, Object> result = new HashMap<>();
            BishengResponse initResponse = initiateWorkflow(flowId);
            if (initResponse.getStatusCode() != 200) {
                log.error("调用智能体工作流失败，错误码: {}, 错误信息: {}", initResponse.getStatusCode(), initResponse.getStatusMessage());
                throw new BishengException(initResponse.getStatusCode(), "调用智能体工作流失败: " + initResponse.getStatusMessage());
            }
            String sessionId = initResponse.getData().getSessionId();
            log.info("智能体工作流会话创建成功，sessionId: {}", sessionId);
            String workflowResult = executeWorkflow(sessionId, flowId, inputJSON, initResponse);
            if (StringUtils.hasText(workflowResult)) {
                workflowResult = workflowResult.replaceAll(" ","");
                result.put("content", workflowResult);
                result.put("status", "success");
            } else {
                result.put("content", "工作流执行成功，但未返回内容");
                result.put("status", "warning");
            }
            return result;
        } catch (IOException e) {
            log.error("调用智能体工作流网络异常: {}", e.getMessage(), e);
            throw new BishengException("调用智能体工作流网络异常: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("调用智能体工作流异常: {}", e.getMessage(), e);
            throw new BishengException("调用智能体工作流异常: " + e.getMessage(), e);
        }
    }
    /**
     * 调用毕昇智能体工作流
     *
     * @param flowId    工作流ID
     * @param userInput 用户输入参数(不包含inputNode)
     * @return 工作流输出结果
     */
    @Override
    public Map<String, Object> invokeWorkflowV2(String flowId, String userInput) {
        if (!StringUtils.hasText(flowId)) {
            throw new BishengException("工作流ID不能为空");
        }
        log.info("调用智能体工作流开始，flowId: {}, 输入参数: {}", flowId, userInput);
        try {
            Map<String, Object> result = new HashMap<>();
            BishengResponse initResponse = initiateWorkflow(flowId);
            String inputNodeKey = "";
            try {
                inputNodeKey = initResponse.getData().getEvents().stream().filter(item -> "input".equals(item.getEvent())).findFirst().get().getNodeId();
            }catch (Exception e){
                log.error("解析inputNode错误");
            }
            JSONObject inputNode = new JSONObject();
            inputNode.put("user_input", userInput);
            JSONObject inputJSON = new JSONObject();
            inputJSON.put(inputNodeKey, inputNode);
            if (initResponse.getStatusCode() != 200) {
                log.error("调用智能体工作流失败，错误码: {}, 错误信息: {}", initResponse.getStatusCode(), initResponse.getStatusMessage());
                throw new BishengException(initResponse.getStatusCode(), "调用智能体工作流失败: " + initResponse.getStatusMessage());
            }
            String sessionId = initResponse.getData().getSessionId();
            log.info("智能体工作流会话创建成功，sessionId: {}", sessionId);
            String workflowResult = executeWorkflow(sessionId, flowId, inputJSON, initResponse);
            if (StringUtils.hasText(workflowResult)) {
                workflowResult = workflowResult.replaceAll(" ","");
                result.put("content", workflowResult);
                result.put("status", "success");
            } else {
                result.put("content", "工作流执行成功，但未返回内容");
                result.put("status", "warning");
            }
            return result;
        } catch (IOException e) {
            log.error("调用智能体工作流网络异常: {}", e.getMessage(), e);
            throw new BishengException("调用智能体工作流网络异常: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("调用智能体工作流异常: {}", e.getMessage(), e);
            throw new BishengException("调用智能体工作流异常: " + e.getMessage(), e);
        }
    }
    /**
     * 初始化工作流调用
     */
    private BishengResponse initiateWorkflow(String flowId) throws IOException {
        JSONObject request = new JSONObject();
        request.put("workflow_id", flowId);
        request.put("stream", false);
        log.info("智能体工作流开始执行请求发起: url:{} 入参：{}", bishengProperties.getInvokeUrl(flowId), JSON.toJSONString(request));
        String response = agentHttpClientUtil.post(bishengProperties.getInvokeUrl(flowId), JSONObject.toJSONString(request));
        log.info("智能体工作流开始执行请求响应: response:{} ", response);
        BishengResponse bishengResponse = JSONObject.parseObject(response, BishengResponse.class);
        return bishengResponse;
    }

    /**
     * 工作流执行
     *
     * @param sessionId
     * @param flowId
     * @param inputJSON
     * @param initResponse
     */
    private String executeWorkflow(String sessionId, String flowId, JSONObject inputJSON, BishengResponse initResponse) throws IOException {
        List<BishengResponse.Event> events = initResponse.getData().getEvents();
        BishengResponse.Event event = events.stream().filter(item -> "input".equals(item.getEvent())).findFirst().get();
        JSONObject request = new JSONObject();
        request.put("workflow_id", flowId);
        request.put("stream", true);
        request.put("input", inputJSON);
        request.put("message_id", event.getMessageId());
        request.put("session_id", sessionId);
        log.info("智能体工作流执行请求开始: url:{} 入参：{}", bishengProperties.getInvokeUrl(flowId), JSON.toJSONString(request));
        String result = agentHttpClientUtil.postStream(bishengProperties.getInvokeUrl(flowId), JSONObject.toJSONString(request));
        log.info("智能体工作流执行完成，返回内容: {}", result);
        return result;
    }
}

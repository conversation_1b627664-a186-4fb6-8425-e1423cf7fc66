package com.pitaya.devdiscovery.bishengagent.service;

import com.alibaba.fastjson2.JSONObject;

import java.util.Map;

/**
 * 毕昇智能体服务接口
 */
public interface BishengAgentService {
    /**
     * 调用毕昇智能体工作流
     *
     * @param flowId   工作流ID
     * @param inputMap 用户输入参数（需要包含inputNode）
     * @return 工作流输出结果
     */
    Map<String, Object> invokeWorkflow(String flowId, JSONObject inputMap);
    /**
     * 调用毕昇智能体工作流
     *
     * @param flowId   工作流ID
     * @param userInput 用户输入参数(不包含inputNode)
     * @return 工作流输出结果
     */
    Map<String, Object> invokeWorkflowV2(String flowId,String userInput);
}

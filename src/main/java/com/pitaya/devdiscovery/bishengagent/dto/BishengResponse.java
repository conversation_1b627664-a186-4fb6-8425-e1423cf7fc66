package com.pitaya.devdiscovery.bishengagent.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 毕昇智能体API响应对象
 */
@Data
public class BishengResponse {
    /**
     * 状态码
     */
    @JSONField(name = "status_code")
    private Integer statusCode;

    /**
     * 状态消息
     */
    @JSONField(name = "status_message")
    private String statusMessage;

    /**
     * 响应数据
     */
    private ResponseData data;

    @Data
    public static class ResponseData {
        /**
         * 会话ID
         */
        @JSONField(name = "session_id")
        private String sessionId;

        /**
         * 事件列表
         */
        private List<Event> events;
    }

    @Data
    public static class Event {
        /**
         * 事件类型
         */
        private String event;

        /**
         * 消息ID
         */
        @JSONField(name = "message_id")
        private String messageId;

        /**
         * 状态
         */
        private String status;

        /**
         * 节点ID
         */
        @JSONField(name = "node_id")
        private String nodeId;

        /**
         * 节点执行ID
         */
        @JSONField(name = "node_execution_id")
        private String nodeExecutionId;

        /**
         * 输出数据
         */
        @JSONField(name = "output_schema")
        private Map<String, Object> outputSchema;

        /**
         * 输入数据
         */
        @JSONField(name = "input_schema")
        private Map<String, Object> inputSchema;
    }
}
package com.pitaya.devdiscovery.aiProductAnalysis.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.aiProductAnalysis.constant.ProductAnalysisConstant;
import com.pitaya.devdiscovery.aiProductAnalysis.dto.BusinessAnalysisDTO;
import com.pitaya.devdiscovery.aiProductAnalysis.entity.BusinessAnalysis;
import com.pitaya.devdiscovery.aiProductAnalysis.service.IBusinessAnalysisService;
import com.pitaya.devdiscovery.aiProductAnalysis.service.IProductAnalysisAgentService;
import com.pitaya.devdiscovery.aiability.config.AIThreadPoolConfig;
import com.pitaya.devdiscovery.bishengagent.service.BishengAgentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class ProductAnalysisAgentServiceImpl implements IProductAnalysisAgentService {
    private static final Logger logger = LoggerFactory.getLogger(ProductAnalysisAgentServiceImpl.class);
    @Autowired
    private BishengAgentService bishengAgentService;
    @Autowired
    private IBusinessAnalysisService businessAnalysisService;
    /**
     * 处理业务分析任务
     */
    @Override
    @Async(AIThreadPoolConfig.TASK_EXECUTOR_NAME)
    public void handleBusinessAnalysis(BusinessAnalysis businessAnalysis) {
        try {
            logger.info("开始处理业务分析任务，ID: {}", businessAnalysis.getId());
            // 先更新状态为"正在处理"
            BusinessAnalysisDTO processingDto = new BusinessAnalysisDTO();
            processingDto.setId(businessAnalysis.getId());
            processingDto.setProcessStatus(ProductAnalysisConstant.STATUS_PROCESSING);
            businessAnalysisService.updateBusinessAnalysis(processingDto);
            logger.info("已更新任务状态为正在处理，ID: {}", businessAnalysis.getId());
            // 构建请求参数
            JSONObject request = new JSONObject();
            request.put("dataType", businessAnalysis.getDataType());
            request.put("name", businessAnalysis.getName());
            request.put("content", businessAnalysis.getContent());
            String query = JSONObject.toJSONString(request);
            logger.info("调用智能体，请求参数: {}", query);
            // 调用智能体
            JSONObject response = invokeProductAnalysisAgent(query);
            logger.info("智能体返回结果: {}", response.toJSONString());
            // 解析结果
            String checkContentStatus = response.getString("checkContentStatus");
            JSONArray keywordsArray = response.getJSONArray("keywords");
            JSONArray productArray = response.getJSONArray("product");
            // 转换为逗号分隔的字符串
            String keywords = "";
            String product = "";
            if (keywordsArray != null && !keywordsArray.isEmpty()) {
                keywords = keywordsArray.stream().map(Object::toString).collect(Collectors.joining(","));
            }
            if (productArray != null && !productArray.isEmpty()) {
                 product = productArray.stream()
                        .map(obj -> {
                            if (obj instanceof JSONObject) {
                                return ((JSONObject) obj).getString("name");
                            }
                            return obj.toString();
                        })
                        .collect(Collectors.joining(","));
            }
            // 更新数据库
            BusinessAnalysisDTO updateDto = new BusinessAnalysisDTO();
            updateDto.setId(businessAnalysis.getId());
            updateDto.setCheckContentStatus(checkContentStatus);
            updateDto.setKeywords(keywords);
            updateDto.setProduct(product);
            updateDto.setProcessStatus(ProductAnalysisConstant.STATUS_PROCESSED);
            businessAnalysisService.updateBusinessAnalysis(updateDto);
            logger.info("业务分析任务处理完成，ID: {}", businessAnalysis.getId());
        } catch (Exception e) {
            logger.error("处理业务分析任务失败，ID: {}", businessAnalysis.getId(), e);
            // 更新失败状态
            BusinessAnalysisDTO updateDto = new BusinessAnalysisDTO();
            updateDto.setId(businessAnalysis.getId());
            updateDto.setProcessStatus(ProductAnalysisConstant.STATUS_FAILED);
            businessAnalysisService.updateBusinessAnalysis(updateDto);
        }
    }

    /**
     * 调用产品分析智能体
     *
     * @param query 查询参数
     * @return 智能体返回结果
     */
    private JSONObject invokeProductAnalysisAgent(String query) {
        JSONObject inputNode = new JSONObject();
        inputNode.put("user_input", query);
        JSONObject input = new JSONObject();
        input.put("input_1b94b", inputNode);
        Map<String, Object> result = bishengAgentService.invokeWorkflow(ProductAnalysisConstant.PRODUCT_ANALYSIS_FLOW_ID, input);
        String content = (String) result.get("content");
        JSONObject contentJSON = JSONObject.parseObject(content);
        Set<String> keys = contentJSON.keySet();
        JSONObject analysisResult = new JSONObject();
        for (String key : keys) {
            String str = contentJSON.getString(key);
            JSONObject obj = JSONObject.parseObject(str);
            analysisResult.putAll(obj);
        }
        return analysisResult;
    }
}
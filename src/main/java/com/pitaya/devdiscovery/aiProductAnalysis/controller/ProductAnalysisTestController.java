package com.pitaya.devdiscovery.aiProductAnalysis.controller;

import com.pitaya.devdiscovery.aiProductAnalysis.dto.BusinessAnalysisDTO;
import com.pitaya.devdiscovery.aiProductAnalysis.entity.BusinessAnalysis;
import com.pitaya.devdiscovery.aiProductAnalysis.service.IBusinessAnalysisService;
import com.pitaya.devdiscovery.aiProductAnalysis.service.IProductAnalysisAgentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/productAnalysisTest")
public class ProductAnalysisTestController {
    private static final Logger logger = LoggerFactory.getLogger(ProductAnalysisTestController.class);
    @Autowired
    private IBusinessAnalysisService businessAnalysisService;
    @Autowired
    private IProductAnalysisAgentService productAnalysisAgentService;
    @RequestMapping("/analyzeBusinessByAI")
    public void analyzeBusinessByAI() {
        logger.info("开始执行产品分析任务...");
        try {
            BusinessAnalysisDTO param = new BusinessAnalysisDTO();
            List<BusinessAnalysis> pendingTasks = businessAnalysisService.selectPendingBusinessAnalysis(param);
            logger.info("查询到待处理任务数量: {}", pendingTasks.size());
            for (BusinessAnalysis task : pendingTasks) {
                logger.info("处理任务: ID={}, 数据类型={}, 名称={}", task.getId(), task.getDataType(), task.getName());
                productAnalysisAgentService.handleBusinessAnalysis(task);
            }
            logger.info("产品分析任务执行完成");
        } catch (Exception e) {
            logger.error("产品分析任务执行失败", e);
        }
    }
}

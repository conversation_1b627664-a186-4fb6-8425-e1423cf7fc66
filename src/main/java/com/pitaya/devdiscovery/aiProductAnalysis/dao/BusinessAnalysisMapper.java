package com.pitaya.devdiscovery.aiProductAnalysis.dao;

import com.pitaya.devdiscovery.aiProductAnalysis.dto.BusinessAnalysisDTO;
import com.pitaya.devdiscovery.aiProductAnalysis.entity.BusinessAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BusinessAnalysisMapper {

    /**
     * 查询待处理的业务分析任务列表
     * 
     * @param param 查询参数
     * @return 待处理任务列表
     */
    List<BusinessAnalysis> selectPendingBusinessAnalysis(@Param("param") BusinessAnalysisDTO param);
    /**
     * 更新业务分析结果
     * 
     * @param param 更新参数
     * @return 更新结果
     */
    int updateBusinessAnalysis(@Param("param") BusinessAnalysisDTO param);
    /**
     * 插入业务分析记录
     * 
     * @param businessAnalysis 业务分析实体
     * @return 插入结果
     */
    int insertBusinessAnalysis(@Param("param") BusinessAnalysis businessAnalysis);
    /**
     * 根据ID查询业务分析记录
     * 
     * @param id 主键ID
     * @return 业务分析记录
     */
    BusinessAnalysis selectById(@Param("id") String id);
}
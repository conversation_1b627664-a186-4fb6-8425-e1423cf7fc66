package com.pitaya.devdiscovery.aiProductAnalysis.schedule;

import com.pitaya.devdiscovery.aiProductAnalysis.dto.BusinessAnalysisDTO;
import com.pitaya.devdiscovery.aiProductAnalysis.entity.BusinessAnalysis;
import com.pitaya.devdiscovery.aiProductAnalysis.service.IBusinessAnalysisService;
import com.pitaya.devdiscovery.aiProductAnalysis.service.IProductAnalysisAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class ProductAnalysisSchedule {
    @Autowired
    private IBusinessAnalysisService businessAnalysisService;
    @Autowired
    private IProductAnalysisAgentService productAnalysisAgentService;
    /**
     * 定时执行产品分析任务
     */
    public void analyzeBusinessByAI() {
        log.info("开始产品分析任务...");
        try {
            BusinessAnalysisDTO param = new BusinessAnalysisDTO();
            List<BusinessAnalysis> pendingTasks = businessAnalysisService.selectPendingBusinessAnalysis(param);
            if (pendingTasks.isEmpty()) {
                log.info("没有待处理的产品分析任务");
                return;
            }
            log.info("查询到待处理任务数量: {}", pendingTasks.size());
            for (BusinessAnalysis task : pendingTasks) {
                log.info("处理任务: ID={}, 数据类型={}, 名称={}", task.getId(), task.getDataType(), task.getName());
                productAnalysisAgentService.handleBusinessAnalysis(task);
            }
            log.info("产品分析任务执行完成");
        } catch (Exception e) {
            log.error("产品分析任务执行失败", e);
        }
    }
}
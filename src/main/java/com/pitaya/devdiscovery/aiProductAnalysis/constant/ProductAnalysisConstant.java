package com.pitaya.devdiscovery.aiProductAnalysis.constant;


public class ProductAnalysisConstant {
    /**
     * 产品分析智能体Flow ID
     */
    public static final String PRODUCT_ANALYSIS_FLOW_ID = "824363763dc94e5baf7d364120c6b866";
    /**
     * 处理状态常量
     */
    public static final String STATUS_PENDING = "0";      // 待处理
    public static final String STATUS_PROCESSING = "1";   // 正在处理
    public static final String STATUS_PROCESSED = "2";    // 已处理
    public static final String STATUS_FAILED = "3";       // 处理失败
    /**
     * 内容状态常量
     */
    public static final String CONTENT_MEANINGFUL = "1";
    public static final String CONTENT_MEANINGLESS = "0";
}
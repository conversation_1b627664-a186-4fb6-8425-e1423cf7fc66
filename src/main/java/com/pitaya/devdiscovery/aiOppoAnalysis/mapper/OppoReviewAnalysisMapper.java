package com.pitaya.devdiscovery.aiOppoAnalysis.mapper;


import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoReviewAnalysisDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoReviewAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;


@Mapper
public interface OppoReviewAnalysisMapper {

    /**
     * 查询待处理的商机评审工单列表
     * @param param
     * @return
     */
    List<OppoReviewAnalysis> selectPendingOppoReview(@Param("param") OppoReviewAnalysisDto param);

    /**
     * 更新商机评审分析结果
     * @param param
     * @return
     */
    int updateOppoReview(@Param("param") OppoReviewAnalysisDto param);
}

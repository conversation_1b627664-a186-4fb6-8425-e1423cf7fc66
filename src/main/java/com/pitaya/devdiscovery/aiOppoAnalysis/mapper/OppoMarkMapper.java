package com.pitaya.devdiscovery.aiOppoAnalysis.mapper;


import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoMarkDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoMarkPools;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface OppoMarkMapper {
    /**
     * 查询待处理的商机标记列表
     * @param dto
     * @return
     */
    List<OppoMarkPools> selectPendingOppoMark(@Param("param") OppoMarkDto dto);
    /**
     * 更新商机标记处理结果
     * @param dto
     * @return
     */
    int updateOppoMarkStatus(@Param("param") OppoMarkDto dto);
}

package com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 商机标记查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-28
 */
@Data
public class OppoMarkDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商机编号
     */
    private String oppoNumber;

    /**
     * 状态(0:未处理 1：已处理)
     */
    private String status;
    /**
     * 商机打标类型（纯硬件或纯联网商机、软硬结合商机 、纯软件商机 ）
     */
    private String markType;
    /**
     * AI推荐产品
     */
    private String productAi;

    /**
     * 需求关键词
     */
    private String reqKeyword;

    /**
     * 账期
     */
    private String dayId;
    /**
     * 是否通过初筛
     */
    private String firstAuditPass;
}

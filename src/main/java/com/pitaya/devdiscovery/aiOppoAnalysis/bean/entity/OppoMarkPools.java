package com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 商机标记池联表查询结果实体
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-28
 */
@Data
public class OppoMarkPools implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商机编号
     */
    private String oppoNumber;

    /**
     * 商机名称
     */
    private String oppoName;

    /**
     * 客户需求简介
     */
    private String khxqjj;
}

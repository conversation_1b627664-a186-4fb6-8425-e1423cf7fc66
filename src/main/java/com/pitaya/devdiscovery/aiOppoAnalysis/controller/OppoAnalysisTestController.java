package com.pitaya.devdiscovery.aiOppoAnalysis.controller;


import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoMarkDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoReviewAnalysisDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoMarkPools;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoReviewAnalysis;
import com.pitaya.devdiscovery.aiOppoAnalysis.service.IOppoAnalysisAgentService;
import com.pitaya.devdiscovery.aiOppoAnalysis.service.IOppoMarkService;
import com.pitaya.devdiscovery.aiOppoAnalysis.service.IOppoReviewAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @program: sanquan_server
 * @ClassName OppoAnalysisTestController
 * @description: 测试
 * @author: majian
 * @date: 2025-07-08 19:07
 * @Version 1.0
 **/
@RestController
@RequestMapping("/oppoAnalysisTest")
public class OppoAnalysisTestController {
    private Logger logger = LoggerFactory.getLogger(OppoAnalysisTestController.class);
    @Autowired
    private IOppoReviewAnalysisService oppoReviewAnalysisService;
    @Autowired
    private IOppoAnalysisAgentService oppoAnalysisAgentService;
    @Autowired
    private IOppoMarkService oppoMarkService;
    /**
     * 商机研判汇总商机跟进建议
     */
    @RequestMapping("/analyzeOppoFollowAdviceByAI")
    public void analyzeOppoFollowAdviceByAI() {
        logger.info("开始执行商机研判汇总商机跟进建议任务...");
        try {
            OppoReviewAnalysisDto param = new OppoReviewAnalysisDto();
            List<OppoReviewAnalysis> pendingTasks = oppoReviewAnalysisService.selectPendingOppoReview(param);
            for (OppoReviewAnalysis task : pendingTasks) {
                oppoAnalysisAgentService.handleOppoReviewAnalysis(task);
            }
            logger.info("商机研判汇总商机跟进建议任务执行完成");
        } catch (Exception e) {
            logger.error("商机研判汇总商机跟进建议任务执行失败", e);
        }
    }
    /**
     * 商机研判匹配自研产品任务
     */
    @RequestMapping("/analyzeOppoMarkByAI")
    public void analyzeOppoMarkByAI() {
        logger.info("开始执行商机研判匹配自研产品任务...");
        try {
            OppoMarkDto param = new OppoMarkDto();
            List<OppoMarkPools> pendingTasks = oppoMarkService.selectPendingOppoMark(param);
            for (OppoMarkPools task : pendingTasks) {
                oppoAnalysisAgentService.handleOppoMarkAnalysis(task);
            }
            logger.info("商机研判匹配自研产品任务执行完成");
        } catch (Exception e) {
            logger.error("商机研判匹配自研产品任务执行失败", e);
        }
    }
}

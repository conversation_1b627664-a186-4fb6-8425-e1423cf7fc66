package com.pitaya.devdiscovery.aiOppoAnalysis.service;


import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoReviewAnalysisDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoReviewAnalysis;
import java.util.List;
public interface IOppoReviewAnalysisService{
    List<OppoReviewAnalysis> selectPendingOppoReview(OppoReviewAnalysisDto param);
    int updateOppoReview(OppoReviewAnalysisDto param);
}

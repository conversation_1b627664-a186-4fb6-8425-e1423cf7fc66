package com.pitaya.devdiscovery.aiOppoAnalysis.service;


import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoMarkPools;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoReviewAnalysis;

public interface IOppoAnalysisAgentService {
    public void handleOppoReviewAnalysis(OppoReviewAnalysis param);

    /**
     * 处理商机标记AI分析
     *
     * @param param 商机标记数据
     */
    public void handleOppoMarkAnalysis(OppoMarkPools param);
}

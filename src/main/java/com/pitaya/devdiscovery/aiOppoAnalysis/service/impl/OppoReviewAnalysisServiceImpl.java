package com.pitaya.devdiscovery.aiOppoAnalysis.service.impl;



import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoReviewAnalysisDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoReviewAnalysis;
import com.pitaya.devdiscovery.aiOppoAnalysis.mapper.OppoReviewAnalysisMapper;
import com.pitaya.devdiscovery.aiOppoAnalysis.service.IOppoReviewAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class OppoReviewAnalysisServiceImpl implements IOppoReviewAnalysisService {
    @Autowired
    private OppoReviewAnalysisMapper oppoReviewAnalysisMapper;
    @Override
    public List<OppoReviewAnalysis> selectPendingOppoReview(OppoReviewAnalysisDto dto) {
        return oppoReviewAnalysisMapper.selectPendingOppoReview(dto);
    }
    @Override
    public int updateOppoReview(OppoReviewAnalysisDto dto) {
        return oppoReviewAnalysisMapper.updateOppoReview(dto);
    }
}

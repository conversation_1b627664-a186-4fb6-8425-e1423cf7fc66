package com.pitaya.devdiscovery.aiOppoAnalysis.service;


import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoMarkDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoMarkPools;
import java.util.List;
public interface IOppoMarkService {

    /**
     * 查询待处理的商机标记列表
     *
     * @param param 查询条件
     * @return 待处理商机标记列表
     */
    List<OppoMarkPools> selectPendingOppoMark(OppoMarkDto param);

    /**
     * 更新商机标记处理状态
     *
     * @param param 更新数据
     * @return 更新结果
     */
    int updateOppoMarkStatus(OppoMarkDto param);
}

package com.pitaya.devdiscovery.aiOppoAnalysis.service.impl;


import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoMarkDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoMarkPools;
import com.pitaya.devdiscovery.aiOppoAnalysis.mapper.OppoMarkMapper;
import com.pitaya.devdiscovery.aiOppoAnalysis.service.IOppoMarkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class OppoMarkServiceImpl implements IOppoMarkService {

    @Autowired
    private OppoMarkMapper oppoMarkMapper;

    @Override
    public List<OppoMarkPools> selectPendingOppoMark(OppoMarkDto param) {
        return oppoMarkMapper.selectPendingOppoMark(param);
    }

    @Override
    public int updateOppoMarkStatus(OppoMarkDto param) {
        return oppoMarkMapper.updateOppoMarkStatus(param);
    }
}

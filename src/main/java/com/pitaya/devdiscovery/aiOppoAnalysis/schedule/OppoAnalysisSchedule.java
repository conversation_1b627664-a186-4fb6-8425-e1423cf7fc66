package com.pitaya.devdiscovery.aiOppoAnalysis.schedule;


import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoMarkDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.dto.OppoReviewAnalysisDto;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoMarkPools;
import com.pitaya.devdiscovery.aiOppoAnalysis.bean.entity.OppoReviewAnalysis;
import com.pitaya.devdiscovery.aiOppoAnalysis.service.IOppoAnalysisAgentService;
import com.pitaya.devdiscovery.aiOppoAnalysis.service.IOppoMarkService;
import com.pitaya.devdiscovery.aiOppoAnalysis.service.IOppoReviewAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class OppoAnalysisSchedule {
    @Autowired
    private IOppoReviewAnalysisService oppoReviewAnalysisService;
    @Autowired
    private IOppoAnalysisAgentService oppoAnalysisAgentService;
    @Autowired
    private IOppoMarkService oppoMarkService;
    /**
     * 商机研判汇总商机跟进建议
     */
    public void analyzeOppoFollowAdviceByAI() {
        log.info("开始执行商机研判汇总商机跟进建议任务...");
        try {
            OppoReviewAnalysisDto param = new OppoReviewAnalysisDto();
            List<OppoReviewAnalysis> pendingTasks = oppoReviewAnalysisService.selectPendingOppoReview(param);
            for (OppoReviewAnalysis task : pendingTasks) {
                oppoAnalysisAgentService.handleOppoReviewAnalysis(task);
            }
            log.info("商机研判汇总商机跟进建议任务执行完成");
        } catch (Exception e) {
            log.error("商机研判汇总商机跟进建议任务执行失败", e);
        }
    }
    /**
     * 商机研判匹配自研产品任务
     */
    public void analyzeOppoMarkByAI() {
        log.info("开始执行商机研判匹配自研产品任务...");
        try {
            OppoMarkDto param = new OppoMarkDto();
            List<OppoMarkPools> pendingTasks = oppoMarkService.selectPendingOppoMark(param);
            for (OppoMarkPools task : pendingTasks) {
                oppoAnalysisAgentService.handleOppoMarkAnalysis(task);
            }
            log.info("商机研判匹配自研产品任务执行完成");
        } catch (Exception e) {
            log.error("商机研判匹配自研产品任务执行失败", e);
        }
    }
}

package com.pitaya.devdiscovery.dsbusinessopportunity.service;

import com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsOpportunityAnalyse;

import java.util.List;

/**
 * 标讯数据服务接口
 */
public interface DsOpportunityAnalyseService {

    /**
     * 根据ID查询标讯信息
     *
     * @param id 主键
     * @return 标讯实体
     */
    DsOpportunityAnalyse findById(Long id);

    /**
     * 根据状态查询标讯列表
     *
     * @param status 状态
     * @return 标讯实体列表
     */
    List<DsOpportunityAnalyse> findByStatus(String status);

    /**
     * 根据ID更新标讯信息（匹配关键词、匹配产品、状态、是否数字化）
     *
     * @param dsOpportunityAnalyse 包含ID、keywords、product、status、digitalFlag的标讯对象
     * @return 是否成功
     */
    boolean updateMatchInfo(DsOpportunityAnalyse dsOpportunityAnalyse);

    /**
     * 根据ID列表批量更新digital_flag
     *
     * @param ids         ID列表
     * @param digitalFlag 数字项目标识
     * @return 是否成功
     */
    boolean batchUpdateDigitalFlagByIds(List<Long> ids, String digitalFlag);

    /**
     * 根据ID列表批量更新状态
     * @param ids
     * @param status
     * @return
     */
    boolean batchUpdateStatusByIds(List<Long> ids, String status);

    /**
     * 批量打标是否数字化项目
     * @param tasks
     */
    void batchProcessMarkDigitalStatus(List<DsOpportunityAnalyse> tasks);

    /**
     * 多线程批量处理标讯任务
     *
     * @param tasks 标讯任务列表
     */
    void batchProcessDigitalTasksAsync(List<DsOpportunityAnalyse> tasks);
}

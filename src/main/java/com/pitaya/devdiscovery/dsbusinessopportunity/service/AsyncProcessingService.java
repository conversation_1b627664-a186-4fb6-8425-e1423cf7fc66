package com.pitaya.devdiscovery.dsbusinessopportunity.service;

import com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsBusinessAnalyse;
import com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsOpportunityAnalyse;
import org.springframework.stereotype.Service;

@Service
public interface AsyncProcessingService {
   void processBusinessAnalyseTask(DsBusinessAnalyse task);
   void processOpportunityAnalyseTask(DsOpportunityAnalyse task);
}

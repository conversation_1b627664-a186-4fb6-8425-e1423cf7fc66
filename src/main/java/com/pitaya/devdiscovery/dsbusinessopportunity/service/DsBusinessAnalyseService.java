package com.pitaya.devdiscovery.dsbusinessopportunity.service;

import com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsBusinessAnalyse;

import java.util.List;

/**
 * 商机数据服务接口
 */
public interface DsBusinessAnalyseService {

    /**
     * 根据ID查询商机信息
     *
     * @param id 主键
     * @return 商机实体
     */
    DsBusinessAnalyse findById(Long id);

    /**
     * 根据状态查询商机列表
     *
     * @param status 状态
     * @return 商机实体列表
     */
    List<DsBusinessAnalyse> findByStatus(String status);

    /**
     * 根据ID更新商机信息（匹配关键词、匹配产品、状态）
     * @param dsBusinessAnalyse
     * @return 是否成功
     */
    boolean updateMatchInfo(DsBusinessAnalyse dsBusinessAnalyse);

    /**
     * 多线程批量处理商机任务
     *
     * @param tasks 商机任务列表
     */
    void batchProcessTasksAsync(List<DsBusinessAnalyse> tasks);
}

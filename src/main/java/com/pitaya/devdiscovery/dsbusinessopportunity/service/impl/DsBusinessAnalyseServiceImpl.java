package com.pitaya.devdiscovery.dsbusinessopportunity.service.impl;

import com.pitaya.devdiscovery.dsbusinessopportunity.constant.BusinessOpportunityConstant;
import com.pitaya.devdiscovery.dsbusinessopportunity.dao.DsBusinessAnalyseMapper;
import com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsBusinessAnalyse;
import com.pitaya.devdiscovery.dsbusinessopportunity.service.AsyncProcessingService;
import com.pitaya.devdiscovery.dsbusinessopportunity.service.DsBusinessAnalyseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 商机数据服务实现类
 */
@Service
public class DsBusinessAnalyseServiceImpl implements DsBusinessAnalyseService {
    private static final Logger logger = LoggerFactory.getLogger(DsBusinessAnalyseServiceImpl.class);
    @Autowired
    private DsBusinessAnalyseMapper dsBusinessAnalyseMapper;
    @Autowired
    private AsyncProcessingService asyncProcessingService;

    private static final int BUSINESS_BATCH_SIZE = 50;
    private static final int BUSINESS_TASK_SUB_BATCH_SIZE = 5;
    private static final long BUSINESS_BATCH_DELAY_MS = 3000;
    private static final long BUSINESS_TASK_DELAY_MS = 200;

    /**
     * 根据ID查询商机信息
     */
    @Override
    public DsBusinessAnalyse findById(Long id) {
        return dsBusinessAnalyseMapper.findById(id);
    }

    /**
     * 根据状态查询商机列表
     */
    @Override
    public List<DsBusinessAnalyse> findByStatus(String status) {
        return dsBusinessAnalyseMapper.findByStatus(status);
    }

    /**
     * 根据ID更新商机信息（匹配关键词、匹配产品、状态）
     */
    @Override
    public boolean updateMatchInfo(DsBusinessAnalyse dsBusinessAnalyse) {
        // 更新前先标记为处理中，如果已经是处理中则不重复标记
        if (dsBusinessAnalyse.getId() != null && (dsBusinessAnalyse.getStatus() == null
                || !BusinessOpportunityConstant.STATUS_PROCESSING.equals(dsBusinessAnalyse.getStatus()))) {
            DsBusinessAnalyse existingTask = findById(dsBusinessAnalyse.getId());
            if (existingTask != null
                    && !BusinessOpportunityConstant.STATUS_PROCESSING.equals(existingTask.getStatus())) {
                dsBusinessAnalyseMapper.updateMatchInfo(new DsBusinessAnalyse().setId(dsBusinessAnalyse.getId())
                        .setStatus(BusinessOpportunityConstant.STATUS_PROCESSING));
            }
        }
        return dsBusinessAnalyseMapper.updateMatchInfo(dsBusinessAnalyse) > 0;
    }

    /**
     * 多线程批量处理商机任务
     * 分批处理，减轻系统压力
     */
    @Override
    public void batchProcessTasksAsync(List<DsBusinessAnalyse> tasks) {
        int totalTasks = tasks.size();
        logger.info("开始处理商机任务，总任务数: {}", totalTasks);

        for (int batchStart = 0; batchStart < totalTasks; batchStart += BUSINESS_BATCH_SIZE) {
            int batchEnd = Math.min(batchStart + BUSINESS_BATCH_SIZE, totalTasks);
            List<DsBusinessAnalyse> currentBatch = tasks.subList(batchStart, batchEnd);

            logger.info("处理商机任务批次 {}/{} ({}%), 当前批次任务数: {}",
                    (batchStart / BUSINESS_BATCH_SIZE) + 1,
                    (totalTasks + BUSINESS_BATCH_SIZE - 1) / BUSINESS_BATCH_SIZE,
                    String.format("%.2f", (batchEnd * 100.0 / totalTasks)),
                    currentBatch.size());

            processBusinessTasksOneByOne(currentBatch);

            // 如果不是最后一批，则等待一段时间再处理下一批
            if (batchEnd < totalTasks) {
                try {
                    logger.info("商机任务批次处理完成，等待{}ms后处理下一批", BUSINESS_BATCH_DELAY_MS);
                    Thread.sleep(BUSINESS_BATCH_DELAY_MS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.error("商机任务处理被中断", e);
                    break;
                }
            }
        }
        logger.info("所有商机任务提交处理完成，总数: {}", totalTasks);
    }

    /**
     * 逐个处理商机任务，异步提交，并控制提交速率
     *
     * @param tasks 待处理的任务列表
     */
    private void processBusinessTasksOneByOne(List<DsBusinessAnalyse> tasks) {
        logger.info("开始逐个异步处理商机任务，本批数量: {}", tasks.size());
        int count = 0;
        for (DsBusinessAnalyse task : tasks) {
            this.updateMatchInfo(new DsBusinessAnalyse().setId(task.getId()).setStatus(BusinessOpportunityConstant.STATUS_PROCESSING));
            asyncProcessingService.processBusinessAnalyseTask(task);
            count++;
            if (count % BUSINESS_TASK_SUB_BATCH_SIZE == 0 || count == tasks.size()) {
                logger.info("商机任务异步提交进度: {}/{}", count, tasks.size());
            }
            try {
                Thread.sleep(BUSINESS_TASK_DELAY_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("商机任务逐个提交处理被中断", e);
                break;
            }
        }
        logger.info("本批次商机任务 ({}) 已全部异步提交。", tasks.size());
    }
}

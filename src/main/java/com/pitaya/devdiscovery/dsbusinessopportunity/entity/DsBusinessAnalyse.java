package com.pitaya.devdiscovery.dsbusinessopportunity.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;

/**
 * 商机数据表实体
 */
@NoArgsConstructor
@AllArgsConstructor
public class DsBusinessAnalyse {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商机编号
     */
    private String businessId;

    /**
     * 商机名称
     */
    private String businessName;

    /**
     * 商机内容
     */
    private String businessContent;

    /**
     * 商机金额
     */
    private String businessAmount;

    /**
     * 匹配关键词
     */
    private String keywords;

    /**
     * 匹配产品
     */
    private String product;

    /**
     * 状态 0：等待处理 1：处理中 2：处理成功 3：处理失败
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public DsBusinessAnalyse setId(Long id) {
        this.id = id;
        return this;
    }

    public String getBusinessId() {
        return businessId;
    }

    public DsBusinessAnalyse setBusinessId(String businessId) {
        this.businessId = businessId;
        return this;
    }

    public String getBusinessName() {
        return businessName;
    }

    public DsBusinessAnalyse setBusinessName(String businessName) {
        this.businessName = businessName;
        return this;
    }

    public String getBusinessContent() {
        return businessContent;
    }

    public DsBusinessAnalyse setBusinessContent(String businessContent) {
        this.businessContent = businessContent;
        return this;
    }

    public String getBusinessAmount() {
        return businessAmount;
    }

    public DsBusinessAnalyse setBusinessAmount(String businessAmount) {
        this.businessAmount = businessAmount;
        return this;
    }

    public String getKeywords() {
        return keywords;
    }

    public DsBusinessAnalyse setKeywords(String keywords) {
        this.keywords = keywords;
        return this;
    }

    public String getProduct() {
        return product;
    }

    public DsBusinessAnalyse setProduct(String product) {
        this.product = product;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public DsBusinessAnalyse setStatus(String status) {
        this.status = status;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DsBusinessAnalyse setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DsBusinessAnalyse setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }
}

package com.pitaya.devdiscovery.dsbusinessopportunity.entity;

import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;

/**
 * 标讯数据表实体
 */
@NoArgsConstructor
@AllArgsConstructor
public class DsOpportunityAnalyse {

    /**
     * 主键
     */
    private Long id;

    /**
     * 标讯编码
     */
    private String opportunityId;

    /**
     * 自然客户
     */
    private String customerId;
    /**
     * 自然客户名称
     */
    private String customerName;
    /**
     * 标讯标题
     */
    private String opportunityTitle;
    /**
     * 标讯内容
     */
    private String opportunityContent;
    /**
     * 招标单位
     */
    private String opportunityUnit;
    /**
     * 招标金额
     */
    private String opportunityAmount;
    /**
     * 匹配关键词
     */
    private String keywords;
    /**
     * 匹配产品
     */
    private String product;
    /**
     * 是否数字化项目标识 0：否 1：是
     */
    private String digitalFlag;
    /**
     * 状态 0：等待处理 1：处理中 2：处理成功 3：处理失败
     */
    private String status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public DsOpportunityAnalyse setId(Long id) {
        this.id = id;
        return this;
    }

    public String getOpportunityId() {
        return opportunityId;
    }

    public DsOpportunityAnalyse setOpportunityId(String opportunityId) {
        this.opportunityId = opportunityId;
        return this;
    }

    public String getCustomerId() {
        return customerId;
    }

    public DsOpportunityAnalyse setCustomerId(String customerId) {
        this.customerId = customerId;
        return this;
    }

    public String getCustomerName() {
        return customerName;
    }

    public DsOpportunityAnalyse setCustomerName(String customerName) {
        this.customerName = customerName;
        return this;
    }

    public String getOpportunityTitle() {
        return opportunityTitle;
    }

    public DsOpportunityAnalyse setOpportunityTitle(String opportunityTitle) {
        this.opportunityTitle = opportunityTitle;
        return this;
    }

    public String getOpportunityContent() {
        return opportunityContent;
    }

    public DsOpportunityAnalyse setOpportunityContent(String opportunityContent) {
        this.opportunityContent = opportunityContent;
        return this;
    }

    public String getOpportunityUnit() {
        return opportunityUnit;
    }

    public DsOpportunityAnalyse setOpportunityUnit(String opportunityUnit) {
        this.opportunityUnit = opportunityUnit;
        return this;
    }

    public String getOpportunityAmount() {
        return opportunityAmount;
    }

    public DsOpportunityAnalyse setOpportunityAmount(String opportunityAmount) {
        this.opportunityAmount = opportunityAmount;
        return this;
    }

    public String getKeywords() {
        return keywords;
    }

    public DsOpportunityAnalyse setKeywords(String keywords) {
        this.keywords = keywords;
        return this;
    }

    public String getProduct() {
        return product;
    }

    public DsOpportunityAnalyse setProduct(String product) {
        this.product = product;
        return this;
    }

    public String getDigitalFlag() {
        return digitalFlag;
    }

    public DsOpportunityAnalyse setDigitalFlag(String digitalFlag) {
        this.digitalFlag = digitalFlag;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public DsOpportunityAnalyse setStatus(String status) {
        this.status = status;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public DsOpportunityAnalyse setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DsOpportunityAnalyse setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }
}

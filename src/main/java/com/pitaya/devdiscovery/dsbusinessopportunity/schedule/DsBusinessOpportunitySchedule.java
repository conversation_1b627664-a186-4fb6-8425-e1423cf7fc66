package com.pitaya.devdiscovery.dsbusinessopportunity.schedule;

import com.pitaya.devdiscovery.dsbusinessopportunity.constant.BusinessOpportunityConstant;
import com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsOpportunityAnalyse;
import com.pitaya.devdiscovery.dsbusinessopportunity.service.DsOpportunityAnalyseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName DsBusinessOpportunitySchedule
 * @description: 定时处理标讯、商机任务
 * @author: majian
 * @date: 2025-05-19 13:34
 * @Version 1.0
 **/
@Component
public class DsBusinessOpportunitySchedule {
    private Logger logger = LoggerFactory.getLogger(DsBusinessOpportunitySchedule.class);
    @Autowired
    private DsOpportunityAnalyseService dsOpportunityAnalyseService;
    //@Scheduled(fixedRate = 5000)
    public void processPendingOpportunityAnalyses() {
        List<DsOpportunityAnalyse> pendingTasks = dsOpportunityAnalyseService.findByStatus(BusinessOpportunityConstant.STATUS_PENDING);
        if (pendingTasks.isEmpty()) {
            logger.info("未查询到待处理标讯任务");
        }
        dsOpportunityAnalyseService.batchProcessMarkDigitalStatus(pendingTasks);
    }
}

package com.pitaya.devdiscovery.dsbusinessopportunity.dao;

import com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsBusinessAnalyse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机数据表Mapper
 */
@Mapper
public interface DsBusinessAnalyseMapper {

    /**
     * 根据ID查询商机信息
     *
     * @param id 主键
     * @return 商机实体
     */
    DsBusinessAnalyse findById(@Param("id") Long id);

    /**
     * 根据状态查询商机列表
     *
     * @param status 状态
     * @return 商机实体列表
     */
    List<DsBusinessAnalyse> findByStatus(@Param("status") String status);

    /**
     * 根据ID更新商机信息（匹配关键词、匹配产品、状态）
     *
     * @param dsBusinessAnalyse 包含ID、keywords、product、status的商机对象
     * @return 更新记录数
     */
    int updateMatchInfo(DsBusinessAnalyse dsBusinessAnalyse);

}
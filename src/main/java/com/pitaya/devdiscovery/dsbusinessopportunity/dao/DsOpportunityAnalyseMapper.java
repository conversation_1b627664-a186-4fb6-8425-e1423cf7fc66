package com.pitaya.devdiscovery.dsbusinessopportunity.dao;

import com.pitaya.devdiscovery.dsbusinessopportunity.entity.DsOpportunityAnalyse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标讯数据表Mapper
 */
@Mapper
public interface DsOpportunityAnalyseMapper {

    /**
     * 根据ID查询标讯信息
     *
     * @param id 主键
     * @return 标讯实体
     */
    DsOpportunityAnalyse findById(@Param("id") Long id);

    /**
     * 根据状态查询标讯列表
     * @param status 状态
     * @return 标讯实体列表
     */
    List<DsOpportunityAnalyse> findByStatus(@Param("status") String status);

    /**
     * 根据ID更新标讯信息（匹配关键词、匹配产品、状态、是否数字化）
     * @return 更新记录数
     */
    int updateMatchInfo(DsOpportunityAnalyse dsOpportunityAnalyse);

    /**
     * 根据ID列表批量更新digital_flag
     *
     * @param ids         ID列表
     * @param digitalFlag 数字项目标识
     * @return 更新记录数
     */
    int batchUpdateDigitalFlagByIds(@Param("ids") List<Long> ids, @Param("digitalFlag") String digitalFlag);

    /**
     * 根据ID列表批量更新status
     * @param ids
     * @param status
     * @return
     */
    int batchUpdateStatusByIds(@Param("ids") List<Long> ids, @Param("status") String status);
}

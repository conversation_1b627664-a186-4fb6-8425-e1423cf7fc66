package com.pitaya.devdiscovery.aiagentcustproductrecommend.dto;

/**
 * 客户打标数据 DTO
 * <AUTHOR>
 */
public class CityCustListMarkDTO {
    /**
     * 自然客户id
     */
    private String natureCustId;
    /**
     * 自然客户名称
     */
    private String natureCustName;
    /**
     * 所属行业
     */
    private String industry;
    /**
     * 联系人
     */
    private String legalRepresentative;
    /**
     * 联系电话
     */
    private String telphone;
    /**
     * 客户地址
     */
    private String address;
    /**
     * 基本信息
     */
    private String baseInfo;
    /**
     * 重大事件
     */
    private String majorEvents;
    /**
     * 整体规划
     */
    private String wholePlan;
    /**
     * 合作机会
     */
    private String cooperateOpportunity;
    /**
     * 需求总结
     */
    private String summary;
    /**
     * 关键词
     */
    private String keywords;
    /**
     * 营销机会
     */
    private String marketingOpportunity;
    /**
     * 智能体响应结果
     */
    private String result;
    /**
     * 备注
     */
    private String remark;
    /**
     * 执行状态 0：等待处理 1：客户洞察执行完成 2：需求结构执行完成 3：匹配产品执行完成
     */
    private String processStatus;
    /**
     * 状态 0：等待处理 1：处理中 2：处理成功 3：处理失败
     */
    private String status;

    public String getNatureCustId() {
        return natureCustId;
    }

    public CityCustListMarkDTO setNatureCustId(String natureCustId) {
        this.natureCustId = natureCustId;
        return this;
    }

    public String getNatureCustName() {
        return natureCustName;
    }

    public CityCustListMarkDTO setNatureCustName(String natureCustName) {
        this.natureCustName = natureCustName;
        return this;
    }

    public String getIndustry() {
        return industry;
    }

    public CityCustListMarkDTO setIndustry(String industry) {
        this.industry = industry;
        return this;
    }

    public String getLegalRepresentative() {
        return legalRepresentative;
    }

    public CityCustListMarkDTO setLegalRepresentative(String legalRepresentative) {
        this.legalRepresentative = legalRepresentative;
        return this;
    }

    public String getTelphone() {
        return telphone;
    }

    public CityCustListMarkDTO setTelphone(String telphone) {
        this.telphone = telphone;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public CityCustListMarkDTO setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getBaseInfo() {
        return baseInfo;
    }

    public CityCustListMarkDTO setBaseInfo(String baseInfo) {
        this.baseInfo = baseInfo;
        return this;
    }

    public String getMajorEvents() {
        return majorEvents;
    }

    public CityCustListMarkDTO setMajorEvents(String majorEvents) {
        this.majorEvents = majorEvents;
        return this;
    }

    public String getWholePlan() {
        return wholePlan;
    }

    public CityCustListMarkDTO setWholePlan(String wholePlan) {
        this.wholePlan = wholePlan;
        return this;
    }

    public String getCooperateOpportunity() {
        return cooperateOpportunity;
    }

    public CityCustListMarkDTO setCooperateOpportunity(String cooperateOpportunity) {
        this.cooperateOpportunity = cooperateOpportunity;
        return this;
    }

    public String getSummary() {
        return summary;
    }

    public CityCustListMarkDTO setSummary(String summary) {
        this.summary = summary;
        return this;
    }

    public String getKeywords() {
        return keywords;
    }

    public CityCustListMarkDTO setKeywords(String keywords) {
        this.keywords = keywords;
        return this;
    }

    public String getMarketingOpportunity() {
        return marketingOpportunity;
    }

    public CityCustListMarkDTO setMarketingOpportunity(String marketingOpportunity) {
        this.marketingOpportunity = marketingOpportunity;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public CityCustListMarkDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public CityCustListMarkDTO setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public CityCustListMarkDTO setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getResult() {
        return result;
    }

    public CityCustListMarkDTO setResult(String result) {
        this.result = result;
        return this;
    }
}

package com.pitaya.devdiscovery.aiagentcustproductrecommend.controller;

import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.dto.CityCustListMarkDTO;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.service.ICityCustListMarkAgentAsyncService;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.service.ICityCustListMarkAgentService;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.service.ICityCustListMarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 调用客户商机推荐智能体跑数据
 */
@Slf4j
@RestController
@RequestMapping("/api/custproductrecommend")
public class CustProductRecommendController {
    @Autowired
    private ICityCustListMarkService cityCustListMarkService;
    @Autowired
    private ICityCustListMarkAgentService cityCustListMarkAgentService;
    @Autowired
    private ICityCustListMarkAgentAsyncService cityCustListMarkAgentAsyncService;
    /**
     * 单独调用商机推荐智能体
     * 结果不入库，只打印
     * @param param
     * @return
     */
    @PostMapping("/singleAnalysis")
    public JSONObject singleBusinessAnalysis (@RequestBody CityCustListMarkDTO param) {
        String query = new StringBuffer(param.getNatureCustId()).append(",").append(param.getNatureCustName()).toString();
        JSONObject analysisResult = cityCustListMarkAgentService.invokeProductRecommendAgent(query);
        return analysisResult;
    }
    /**
     * 单独提交商机推荐分析任务 （异步）
     * 结果入库
     * @param param
     * @return
     */
    @PostMapping("/submitSingleAnalysisTask")
    public JSONObject submitSingleAnalysisTask(@RequestBody CityCustListMarkDTO param) {
        cityCustListMarkService.saveCityCustListMark(param);
        CityCustListMark task = new CityCustListMark();
        BeanUtils.copyProperties(param, task);
        cityCustListMarkAgentAsyncService.handleProductRecommendTaskAsync(task);
        JSONObject result = new JSONObject(){{
            put("message", "提交成功");
        }};
        return result;
    }

    /**
     * 批量提交商机推荐分析任务 （异步）
     * 结果入库
     * @return
     */
    @PostMapping("/batchSubmitSingleAnalysisTask")
    public JSONObject batchSubmitSingleAnalysisTask() {
        List<CityCustListMark> pendingTaskList = cityCustListMarkService.getPendingTaskList();
        for(CityCustListMark task : pendingTaskList){
            cityCustListMarkAgentAsyncService.handleProductRecommendTaskAsync(task);
        }
        JSONObject result = new JSONObject(){{
            put("message", "成功提交"+pendingTaskList.size()+"个任务");
        }};
        return result;
    }
}

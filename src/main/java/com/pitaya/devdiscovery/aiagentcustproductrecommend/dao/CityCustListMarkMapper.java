package com.pitaya.devdiscovery.aiagentcustproductrecommend.dao;

import com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
/**
 * 客户打标数据表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CityCustListMarkMapper {
    /**
     * 查询待处理任务
     * @return
     */
    List<CityCustListMark> getPendingTaskList();
    /**
     * 判断是否存在相同custid数据
     * @param natureCustId
     * @return
     */
    int verifyCustMarkExist(@Param("natureCustId") String natureCustId);
    /**
     * 根据 natureCustId 查询实体信息
     *
     * @param natureCustId 自然客户id
     * @return CityCustListMark
     */
    CityCustListMark getByNatureCustId(@Param("natureCustId") String natureCustId);
    /**
     * 插入基础信息
     *
     * @param param CityCustListMark实体
     * @return 影响行数
     */
    int insert(CityCustListMark param);
    /**
     * 根据 natureCustId 更新其他信息 (除id, natureCustId, createTime, updateTime 之外的字段)
     *
     * @param param CityCustListMark实体，其中 natureCustId 用于定位记录，其他字段为待更新值
     * @return 影响行数
     */
    int updateByNatureCustId(CityCustListMark param);
    /**
     * 更新状态
     * @param param
     * @return
     */
    int updateStatusByNatureCustId(CityCustListMark param);
}

package com.pitaya.devdiscovery.aiagentcustproductrecommend.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户打标数据表 实体类
 *
 * <AUTHOR>
 */

public class CityCustListMark implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 自然客户id
     */
    private String natureCustId;

    /**
     * 自然客户名称
     */
    private String natureCustName;
    /**
     * 所属行业
     */
    private String industry;
    /**
     * 联系人
     */
    private String legalRepresentative;
    /**
     * 联系电话
     */
    private String telphone;
    /**
     * 客户地址
     */
    private String address;
    /**
     * 基本信息
     */
    private String baseInfo;

    /**
     * 重大事件
     */
    private String majorEvents;

    /**
     * 整体规划
     */
    private String wholePlan;

    /**
     * 合作机会
     */
    private String cooperateOpportunity;
    /**
     * 需求总结
     */
    private String summary;
    /**
     * 关键词
     */
    private String keywords;
    /**
     * 营销机会
     */
    private String marketingOpportunity;
    /**
     * 智能体响应结果
     */
    private String result;
    /**
     * 执行状态 0：等待处理 1：客户洞察执行完成 2：需求结构执行完成 3：匹配产品执行完成
     */
    private String processStatus;
    /**
     * 状态 0：等待处理 1：处理中 2：处理成功 3：处理失败
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 数据插入时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public CityCustListMark setId(Long id) {
        this.id = id;
        return this;
    }

    public String getNatureCustId() {
        return natureCustId;
    }

    public CityCustListMark setNatureCustId(String natureCustId) {
        this.natureCustId = natureCustId;
        return this;
    }

    public String getNatureCustName() {
        return natureCustName;
    }

    public CityCustListMark setNatureCustName(String natureCustName) {
        this.natureCustName = natureCustName;
        return this;
    }

    public String getIndustry() {
        return industry;
    }

    public CityCustListMark setIndustry(String industry) {
        this.industry = industry;
        return this;
    }

    public String getLegalRepresentative() {
        return legalRepresentative;
    }

    public CityCustListMark setLegalRepresentative(String legalRepresentative) {
        this.legalRepresentative = legalRepresentative;
        return this;
    }

    public String getTelphone() {
        return telphone;
    }

    public CityCustListMark setTelphone(String telphone) {
        this.telphone = telphone;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public CityCustListMark setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getBaseInfo() {
        return baseInfo;
    }

    public CityCustListMark setBaseInfo(String baseInfo) {
        this.baseInfo = baseInfo;
        return this;
    }

    public String getMajorEvents() {
        return majorEvents;
    }

    public CityCustListMark setMajorEvents(String majorEvents) {
        this.majorEvents = majorEvents;
        return this;
    }

    public String getWholePlan() {
        return wholePlan;
    }

    public CityCustListMark setWholePlan(String wholePlan) {
        this.wholePlan = wholePlan;
        return this;
    }

    public String getCooperateOpportunity() {
        return cooperateOpportunity;
    }

    public CityCustListMark setCooperateOpportunity(String cooperateOpportunity) {
        this.cooperateOpportunity = cooperateOpportunity;
        return this;
    }

    public String getSummary() {
        return summary;
    }

    public CityCustListMark setSummary(String summary) {
        this.summary = summary;
        return this;
    }

    public String getKeywords() {
        return keywords;
    }

    public CityCustListMark setKeywords(String keywords) {
        this.keywords = keywords;
        return this;
    }

    public String getMarketingOpportunity() {
        return marketingOpportunity;
    }

    public CityCustListMark setMarketingOpportunity(String marketingOpportunity) {
        this.marketingOpportunity = marketingOpportunity;
        return this;
    }

    public String getResult() {
        return result;
    }

    public CityCustListMark setResult(String result) {
        this.result = result;
        return this;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public CityCustListMark setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public CityCustListMark setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public CityCustListMark setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public CityCustListMark setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public CityCustListMark setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }
}

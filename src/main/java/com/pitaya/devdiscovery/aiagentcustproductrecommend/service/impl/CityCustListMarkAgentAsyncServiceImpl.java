package com.pitaya.devdiscovery.aiagentcustproductrecommend.service.impl;


import com.pitaya.devdiscovery.aiability.config.AIThreadPoolConfig;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.service.ICityCustListMarkAgentAsyncService;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.service.ICityCustListMarkAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @program: sanquan_server
 * @ClassName ICityCustListMarkAgentAsyncServiceImpl
 * @description:
 * @author: majian
 * @date: 2025-05-22 14:51
 * @Version 1.0
 **/
@Service
public class CityCustListMarkAgentAsyncServiceImpl implements ICityCustListMarkAgentAsyncService {
    @Autowired
    private ICityCustListMarkAgentService cityCustListMarkAgentService;

    /**
     * 异步处理调用商机处理智能体任务
     * @param param
     */
    @Override
    @Async(AIThreadPoolConfig.BUSINESS_RECOMMEND_TASK_EXECUTOR_NAME)
    public void handleProductRecommendTaskAsync(CityCustListMark param) {
        cityCustListMarkAgentService.handleProductRecommendTask(param);
    }
}

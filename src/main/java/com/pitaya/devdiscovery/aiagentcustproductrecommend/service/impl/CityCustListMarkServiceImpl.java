package com.pitaya.devdiscovery.aiagentcustproductrecommend.service.impl;


import com.pitaya.devdiscovery.aiagentcustproductrecommend.dao.CityCustListMarkMapper;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.dto.CityCustListMarkDTO;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.service.ICityCustListMarkService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 客户打标数据表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class CityCustListMarkServiceImpl implements ICityCustListMarkService {
    @Autowired
    private CityCustListMarkMapper cityCustListMarkMapper;

    @Override
    public List<CityCustListMark> getPendingTaskList() {
        return cityCustListMarkMapper.getPendingTaskList();
    }

    @Override
    public CityCustListMark getCityCustListMarkByNatureCustId(String natureCustId) {
        if (!StringUtils.hasText(natureCustId)) {
            return null;
        }
        CityCustListMark cityCustListMark = cityCustListMarkMapper.getByNatureCustId(natureCustId);
        return cityCustListMark;
    }

    @Override
    public boolean saveCityCustListMark(CityCustListMarkDTO param) {
        if (param == null || !StringUtils.hasText(param.getNatureCustId())) {
            return false;
        }
        CityCustListMark entity = new CityCustListMark();
        BeanUtils.copyProperties(param, entity);
        if (cityCustListMarkMapper.verifyCustMarkExist(param.getNatureCustId()) > 0) {
            return  cityCustListMarkMapper.updateByNatureCustId(entity) > 0;
        }
        return cityCustListMarkMapper.insert(entity) > 0;
    }

    @Override
    public boolean updateCityCustListMarkByNatureCustId(CityCustListMarkDTO saveDTO) {
        if (saveDTO == null || !StringUtils.hasText(saveDTO.getNatureCustId())) {
            return false;
        }
        if (cityCustListMarkMapper.verifyCustMarkExist(saveDTO.getNatureCustId()) <= 0) {
            return false;
        }
        CityCustListMark entityToUpdate = new CityCustListMark();
        BeanUtils.copyProperties(saveDTO, entityToUpdate);
        return cityCustListMarkMapper.updateByNatureCustId(entityToUpdate) > 0;
    }

    /**
     * 更新客户打标数据表状态
     * @param param
     * @return
     */
    @Override
    public boolean updateStatusByNatureCustId(CityCustListMark param) {
        return cityCustListMarkMapper.updateStatusByNatureCustId(param) > 0;
    }
}

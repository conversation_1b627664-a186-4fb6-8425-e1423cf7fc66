package com.pitaya.devdiscovery.aiagentcustproductrecommend.service;


import com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark;

/**
 * @program: sanquan_server
 * @ClassName ICityCustListMarkAgentAsyncService
 * @description:
 * @author: ma<PERSON><PERSON>
 * @date: 2025-05-22 14:50
 * @Version 1.0
 **/
public interface ICityCustListMarkAgentAsyncService {
    /**
     * 异步处理调用商机推荐智能体任务
     * @param param
     */
    void handleProductRecommendTaskAsync(CityCustListMark param);
}

package com.pitaya.devdiscovery.aiagentcustproductrecommend.service;


import com.pitaya.devdiscovery.aiagentcustproductrecommend.dto.CityCustListMarkDTO;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark;
import java.util.List;
/**
 * 客户打标数据表 服务类接口
 *
 * <AUTHOR>
 */
public interface ICityCustListMarkService {

    /**
     * 查询待处理任务
     */
    List<CityCustListMark> getPendingTaskList();

    /**
     * 根据 natureCustId 查询实体信息
     *
     * @param natureCustId 自然客户id
     * @return CityCustListMark
     */
    CityCustListMark getCityCustListMarkByNatureCustId(String natureCustId);

    /**
     * 插入基础信息
     *
     * @param param
     * @return boolean
     */
    boolean saveCityCustListMark(CityCustListMarkDTO param);

    /**
     * 根据 natureCustId 更新其他信息
     *
     * @param param
     * @return boolean
     */
    boolean updateCityCustListMarkByNatureCustId(CityCustListMarkDTO param);

    /**
     * 根据 natureCustId 更新状态
     * @param param
     * @return
     */
    boolean updateStatusByNatureCustId(CityCustListMark param);
}

package com.pitaya.devdiscovery.aiagentcustproductrecommend.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.aiability.service.RagRecommendProductService;
import com.pitaya.devdiscovery.bishengagent.service.BishengAgentService;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.constant.CityCustListMarkConstant;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.dto.CityCustListMarkDTO;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.entity.CityCustListMark;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.service.ICityCustListMarkAgentService;
import com.pitaya.devdiscovery.aiagentcustproductrecommend.service.ICityCustListMarkService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: sanquan_server
 * @ClassName CityCustListMarkAgentServiceImpl
 * @description:
 * @author: majian
 * @date: 2025-05-22 14:21
 * @Version 1.0
 **/
@Service
public class CityCustListMarkAgentServiceImpl implements ICityCustListMarkAgentService {
    private static Logger logger = LoggerFactory.getLogger(CityCustListMarkAgentServiceImpl.class);
    @Autowired
    private BishengAgentService bishengAgentService;
    @Autowired
    private RagRecommendProductService ragRecommendProductService;
    @Autowired
    private ICityCustListMarkService cityCustListMarkService;
    private static final String DEFAULT_SPLIT_REGEX = ",";

    /**
     * 处理商机推荐任务
     *
     * @param param
     */
    @Override
    public void handleProductRecommendTask(CityCustListMark param) {
        long startTime = System.currentTimeMillis();
        String natureCustId = param.getNatureCustId();
        String query = new StringBuffer(param.getNatureCustId()).append(",").append(param.getNatureCustName()).toString();
        cityCustListMarkService.updateStatusByNatureCustId(new CityCustListMark().setNatureCustId(natureCustId).setStatus(CityCustListMarkConstant.STATUS_PROCESSING));
        logger.info("提交智能体任务 query:{}", query);
        JSONObject res = invokeProductRecommendAgent(query);
        logger.info("res:{}", res);
        cityCustListMarkService.updateStatusByNatureCustId(new CityCustListMark().setNatureCustId(natureCustId).setProcessStatus(CityCustListMarkConstant.PROCESS_STATUS_KHDX_SUCCESS));
        cityCustListMarkService.updateStatusByNatureCustId(new CityCustListMark().setNatureCustId(natureCustId).setProcessStatus(CityCustListMarkConstant.PROCESS_STATUS_XQJG_SUCCESS));
        List<String> keywords = res.getJSONArray("keywords").toList(String.class);
        String productNameStr = "";
        if(res.containsKey("product") && res.getJSONArray("product") != null && res.getJSONArray("product").size() > 0){
            List<String> product = res.getJSONArray("product").toList(String.class);
            productNameStr = product.stream().collect(Collectors.joining(DEFAULT_SPLIT_REGEX));
        }
        //LinkedHashSet<String> productNameSet = ragRecommendProductService.queryRecommendProductByKeywords(keywords);
        //剔除存量产品
        //productNameSet = this.removeExistingProducts(productNameSet, param.getNatureCustName(), 0.6d);
        // 针对客户打标数据解析&落库
        JSONObject khdc = res.getJSONObject("khdc");
        String ywxq = res.getString("ywxq");
        JSONObject ztgh = res.getJSONObject("ztgh");
        CityCustListMarkDTO markDTO = new CityCustListMarkDTO().setNatureCustId(param.getNatureCustId()).setStatus(CityCustListMarkConstant.STATUS_SUCCESS).setProcessStatus(CityCustListMarkConstant.PROCESS_STATUS_PPCP_SUCCESS);
        markDTO.setBaseInfo(JSONObject.toJSONString(khdc.getJSONObject("客户基本信息")));
        markDTO.setMajorEvents(JSONObject.toJSONString(khdc.getJSONObject("公司大事件")));
        markDTO.setCooperateOpportunity(JSONObject.toJSONString(khdc.getJSONObject("合作机会")));
        markDTO.setWholePlan(JSONObject.toJSONString(ztgh));
        markDTO.setSummary(ywxq);
        markDTO.setKeywords(keywords.stream().collect(Collectors.joining(DEFAULT_SPLIT_REGEX)));
        markDTO.setMarketingOpportunity(productNameStr);
        markDTO.setResult(JSONObject.toJSONString(res));
        cityCustListMarkService.updateCityCustListMarkByNatureCustId(markDTO);
        long totalCost = System.currentTimeMillis() - startTime;
        logger.info("任务 taskId:{} 处理完成，总耗时 {} ms ({}s)", param.getId(), totalCost, totalCost / 1000.0);
    }

    /**
     * 调用商机智能体
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject invokeProductRecommendAgent(String query) {
        JSONObject inputNode = new JSONObject();
        inputNode.put("user_input", query);
        JSONObject input = new JSONObject();
        input.put("input_08e64", inputNode);
        Map<String, Object> result = bishengAgentService
                .invokeWorkflow(CityCustListMarkConstant.BUSINESS_RECOMMEND_FLOW_ID, input);
        String content = (String) result.get("content");
        JSONObject contentJSON = JSONObject.parseObject(content);
        Set<String> keys = contentJSON.keySet();
        JSONObject analysisResult = new JSONObject();
        for (String key : keys) {
            String str = contentJSON.getString(key);
            JSONObject obj = JSONObject.parseObject(str);
            analysisResult.putAll(obj);
        }
        return analysisResult;
    }
    /**
     * 剔除存量产品
     * @param productNameSet
     * @param natureCustName
     * @param similarity
     * @return
     */
    private LinkedHashSet<String> removeExistingProducts(LinkedHashSet<String> productNameSet,String natureCustName,Double similarity) {
        Set<String> existProductSet = ragRecommendProductService.queryExistingProductsByCustomerName(natureCustName);
        LinkedHashSet<String> precisionProductNameSet = ragRecommendProductService.removeExistingProducts(productNameSet, existProductSet, similarity);
        return precisionProductNameSet;
    }
}

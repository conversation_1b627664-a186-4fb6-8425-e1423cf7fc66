package com.pitaya.devdiscovery.aiagentcustproductrecommend.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @program: ds-recommend-product
 * @ClassName RegularAnalysisUtils
 * @description:
 * @author: ma<PERSON><PERSON>
 * @date: 2025-04-21 16:02
 * @Version 1.0
 **/
public class RegularAnalysisUtils {
    private static final String DEFAULT_SPLIT_REGEX = "，";
    /**
     * 解析出产品名称
     * @param input
     * @return
     */
    public static String parsingProductName(String input) {
        // 通用正则：匹配各种格式的产品名称
        // '?产品名称'?\s*:\s*'?([^'\r\n,}]*?)'?
        String regex = "'?产品名称'?\\s*:\\s*'?([^'\\r\\n,}]*?)'?(?=[,}\\r\\n]|$)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String productName = matcher.group(1).trim();
            if (StringUtils.isNotEmpty(productName) && !"nan".equalsIgnoreCase(productName)) {
                return productName;
            }
        }
        return null;
    }

    /**
     * 解析存量业务
     * @param input
     * @return
     */
    public static String parsingExistProductName(String input) {
        // 使用正则表达式匹配'存量产品'后的单引号内容
        String regex = "'存量业务':\\s*'([^']*)'";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String productStr = matcher.group(1);
            return productStr;
        }
        return null;
    }
    /**
     * 解析存量业务类型
     * @param input
     * @return
     */
    public static String parsingExistProductType(String input) {
        // 使用正则表达式匹配'存量产品'后的单引号内容
        String regex = "'存量业务类型':\\s*'([^']*)'";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String productStr = matcher.group(1);
            return productStr;
        }
        return null;
    }
    /**
     * 解析出关键字
     * @param input
     * @return
     */
    public static String parsingKeywords(String input) {
        List<String> keywords = new ArrayList<>();
        for (String line : input.split("\\r?\\n")) {
            if(StringUtils.isEmpty(line) || line.contains("根据")||line.contains("优先级")||line.contains("关键字")){
                continue;
            }
            line = line.replaceAll("\\d+\\.\\s*","").replaceAll("\\*", "").trim();
            keywords.add(line);
        }
        return String.join(DEFAULT_SPLIT_REGEX, keywords);
    }
}

package com.pitaya.devdiscovery.aiagentcustproductrecommend.constant;

/**
 * @program: sanquan_server
 * @ClassName CityCustListMarkConstant
 * @description:
 * @author: majian
 * @date: 2025-05-22 14:22
 * @Version 1.0
 **/
public class CityCustListMarkConstant {
    //public static final String BUSINESS_RECOMMEND_FLOW_ID = "253b02f43e864cc5909debda046c1ca8";
    public static final String BUSINESS_RECOMMEND_FLOW_ID = "0b350c59c6614703b6e0d9da87ad2e34";
    public static final String STATUS_WAITING = "0";
    public static final String STATUS_PROCESSING = "1";
    public static final String STATUS_SUCCESS = "2";
    public static final String STATUS_FAILED = "3";

    public static final String PROCESS_STATUS_WAITING = "0";
    //客户洞察执行完成
    public static final String PROCESS_STATUS_KHDX_SUCCESS = "1";
    //需求结构执行完成
    public static final String PROCESS_STATUS_XQJG_SUCCESS = "2";
    //匹配产品执行完成
    public static final String PROCESS_STATUS_PPCP_SUCCESS = "3";
}

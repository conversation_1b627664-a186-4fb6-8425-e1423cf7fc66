# 项目列表模块

## 模块概述

该模块用于处理项目名单数据，实现了基于定时任务的批量数据处理功能。支持线程池并发处理，提高数据处理效率。

## 功能特性

- ✅ 基于Spring Boot的模块化架构
- ✅ MyBatis数据持久化
- ✅ 定时任务自动处理
- ✅ 线程池并发处理
- ✅ 批量数据处理（每批10条，可配置）
- ✅ 状态跟踪和管理
- ✅ REST API接口支持
- ✅ 完整的错误处理和日志记录

## 数据表结构

```sql
CREATE TABLE `yantai_project_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `list_type` varchar(50) NOT NULL COMMENT '名单类型',
  `full_project_name` varchar(255) NOT NULL COMMENT '项目全名称',
  `parsed_company_name` varchar(255) NOT NULL COMMENT '解析公司名称',
  `parsed_project_name` varchar(255) NOT NULL COMMENT '解析项目名称',
  `district` varchar(100) NOT NULL COMMENT '归属区县',
  `status` varchar(100) NOT NULL COMMENT '状态 0：未执行 1：正在执行 2：执行成功 3：执行失败',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_district` (`district`),
  KEY `idx_list_type` (`list_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目名单表';
```

## 状态管理

- `0`: 未执行
- `1`: 正在执行  
- `2`: 执行成功
- `3`: 执行失败

## 配置参数

在 `ProjectConstants` 类中定义了以下可配置参数：

- `BATCH_SIZE = 10`: 每个任务处理的数据条数
- `CORE_POOL_SIZE = 5`: 线程池核心线程数
- `MAX_POOL_SIZE = 10`: 线程池最大线程数
- `QUEUE_CAPACITY = 100`: 线程池队列容量
- `TASK_PROCESS_TIME_MS = 5000`: 任务处理模拟时间（5秒）

## API接口

### 1. 获取所有项目列表
```
GET /api/project/list
```

### 2. 手动触发项目处理任务
```
POST /api/project/process
```

### 3. 通过定时任务触发项目处理
```
POST /api/project/schedule-trigger
```

### 4. 健康检查
```
GET /api/project/health
```

## 定时任务配置

定时任务默认配置为每5分钟执行一次，当前已注释掉：

```java
//@Scheduled(cron = "0 */5 * * * ?")
```

要启用定时任务，请取消注释即可。

## 处理流程

1. **数据获取**: 从数据库获取状态为"未执行"的项目，每批最多10条
2. **状态更新**: 将获取到的项目状态更新为"正在执行"
3. **并发处理**: 使用线程池并发处理项目数据
4. **任务模拟**: 每个项目使用Thread.sleep(5秒)模拟处理过程
5. **状态同步**: 处理完成后批量更新状态为"执行成功"
6. **异常处理**: 如果处理过程中出现异常，状态更新为"执行失败"
7. **循环处理**: 继续处理下一批数据，直到没有待处理的项目

## 线程池配置

使用专用的线程池配置类 `ThreadPoolConfig`：

- 核心线程数：5
- 最大线程数：10  
- 队列容量：100
- 拒绝策略：CallerRunsPolicy（调用者运行）
- 线程命名：project-task-{threadId}

## 使用示例

### 启动定时任务
1. 在 `ProjectListSchedule` 类中取消 `@Scheduled` 注解的注释
2. 重启应用程序，定时任务将自动运行

### 手动触发任务
```bash
# 通过API手动触发
curl -X POST http://localhost:8080/api/project/process

# 或通过定时任务方式触发
curl -X POST http://localhost:8080/api/project/schedule-trigger
```

### 查看处理结果
```bash
# 获取所有项目列表
curl http://localhost:8080/api/project/list
```

## 扩展说明

- 可以通过修改 `ProjectConstants` 中的常量来调整处理参数
- 可以在 `processIndividualProject` 方法中替换 `Thread.sleep()` 为实际的业务逻辑
- 支持通过修改定时任务的cron表达式来调整执行频率
- 线程池参数可以根据实际需求进行调优

## 注意事项

1. 确保数据库表已正确创建
2. 定时任务默认是注释状态，需要手动启用
3. 线程池资源会被Spring容器管理，无需手动释放
4. 建议在生产环境中根据实际负载调整线程池参数
5. 处理大量数据时建议监控系统资源使用情况
6. 虽然代码已通用化，但仍使用原有的 `yantai_project_list` 表名 
package com.pitaya.devdiscovery.projectlist.constant;

/**
 * @program: dev-discovery
 * @ClassName ProjectConstants
 * @description: 项目相关常量定义
 * @author: system
 * @date: 2024-12-19
 * @Version 1.0
 **/
public class ProjectConstants {

    /**
     * 每个任务处理的数据条数
     */
    public static final int BATCH_SIZE = 10;

    /**
     * 线程池核心线程数
     */
    public static final int CORE_POOL_SIZE = 5;

    /**
     * 线程池最大线程数
     */
    public static final int MAX_POOL_SIZE = 10;

    /**
     * 线程池队列容量
     */
    public static final int QUEUE_CAPACITY = 100;

    /**
     * 任务处理模拟时间（毫秒）
     */
    public static final long TASK_PROCESS_TIME_MS = 5000;

    /**
     * 状态：未执行
     */
    public static final String STATUS_PENDING = "0";

    /**
     * 状态：正在执行
     */
    public static final String STATUS_PROCESSING = "1";

    /**
     * 状态：执行成功
     */
    public static final String STATUS_SUCCESS = "2";

    /**
     * 状态：执行失败
     */
    public static final String STATUS_FAILED = "3";
}
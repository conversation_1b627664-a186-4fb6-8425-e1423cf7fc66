package com.pitaya.devdiscovery.projectlist.controller;

import com.pitaya.devdiscovery.projectlist.entity.ProjectList;
import com.pitaya.devdiscovery.projectlist.schedule.ProjectListSchedule;
import com.pitaya.devdiscovery.projectlist.service.ProjectListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName ProjectListController
 * @description: 项目名单控制器
 * @author: system
 * @date: 2024-12-19
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/project")
public class ProjectListController {

    @Autowired
    private ProjectListService projectListService;

    @Autowired
    private ProjectListSchedule projectListSchedule;

    /**
     * 获取所有项目列表
     *
     * @return 所有项目列表
     */
    @GetMapping("/list")
    public List<ProjectList> getAllProjects() {
        log.info("获取所有项目列表");
        return projectListService.getAllProjects();
    }

    /**
     * 手动触发项目处理任务
     *
     * @return 处理结果
     */
    @PostMapping("/process")
    public String processProjects() {
        log.info("手动触发项目处理任务");
        try {
            int processedCount = projectListService.processProjectTasks();
            String result = "成功处理 " + processedCount + " 个项目任务";
            log.info("手动触发任务完成: {}", result);
            return result;
        } catch (Exception e) {
            log.error("手动触发任务失败", e);
            return "任务处理失败: " + e.getMessage();
        }
    }

    /**
     * 通过定时任务触发项目处理
     *
     * @return 处理结果
     */
    @PostMapping("/schedule-trigger")
    public String scheduleProcessProjects() {
        log.info("通过定时任务触发项目处理");
        try {
            projectListSchedule.manualTriggerTask();
            return "定时任务触发成功";
        } catch (Exception e) {
            log.error("定时任务触发失败", e);
            return "定时任务触发失败: " + e.getMessage();
        }
    }

    /**
     * 健康检查接口
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    public String health() {
        return "项目模块运行正常";
    }
}
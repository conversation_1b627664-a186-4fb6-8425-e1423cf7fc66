package com.pitaya.devdiscovery.projectlist.service;

import com.pitaya.devdiscovery.projectlist.constant.ProjectConstants;
import com.pitaya.devdiscovery.projectlist.dao.ProjectListMapper;
import com.pitaya.devdiscovery.projectlist.entity.ProjectList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * @program: dev-discovery
 * @ClassName ProjectListService
 * @description: 项目名单业务服务类
 * @author: system
 * @date: 2024-12-19
 * @Version 1.0
 **/
@Slf4j
@Service
public class ProjectListService {

    @Autowired
    private ProjectListMapper projectListMapper;

    /**
     * 注入Spring管理的线程池
     */
    @Autowired
    @Qualifier("projectTaskExecutor")
    private Executor taskExecutor;

    /**
     * 处理项目任务
     * 
     * @return 处理的任务数量
     */
    public int processProjectTasks() {
        log.info("开始处理项目任务，每批处理{}条数据", ProjectConstants.BATCH_SIZE);

        int totalProcessed = 0;

        // 循环处理所有待处理的项目
        while (true) {
            // 获取待处理的项目列表
            List<ProjectList> pendingProjects = projectListMapper.selectPendingProjects(
                    ProjectConstants.STATUS_PENDING,
                    ProjectConstants.BATCH_SIZE);

            if (pendingProjects.isEmpty()) {
                break;
            }

            log.info("本批次获取到{}条待处理项目", pendingProjects.size());

            // 提交任务到线程池处理
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                processProjectBatch(pendingProjects);
            }, taskExecutor);

            // 等待当前批次完成（也可以选择不等待，让多个批次并发执行）
            try {
                future.get();
                totalProcessed += pendingProjects.size();
                log.info("当前批次{}条项目处理完成", pendingProjects.size());
            } catch (Exception e) {
                log.error("等待批次处理完成时发生异常", e);
            }
        }

        log.info("所有项目任务处理完成，共处理{}条数据", totalProcessed);
        return totalProcessed;
    }

    /**
     * 处理一批项目数据
     * 
     * @param projectList 项目列表
     */
    private void processProjectBatch(List<ProjectList> projectList) {
        log.info("开始处理批次，包含{}条项目数据", projectList.size());

        // 批量更新状态为正在执行
        List<Integer> projectIds = projectList.stream()
                .map(ProjectList::getId)
                .collect(Collectors.toList());

        try {
            // 更新状态为正在执行
            projectListMapper.batchUpdateStatus(projectIds, ProjectConstants.STATUS_PROCESSING);
            log.info("批量更新{}条项目状态为正在执行", projectIds.size());

            // 遍历处理每个项目
            for (ProjectList project : projectList) {
                processIndividualProject(project);
            }

            // 批量更新状态为执行成功
            projectListMapper.batchUpdateStatus(projectIds, ProjectConstants.STATUS_SUCCESS);
            log.info("批量更新{}条项目状态为执行成功", projectIds.size());

        } catch (Exception e) {
            log.error("处理批次时发生异常，将状态更新为失败", e);
            // 批量更新状态为执行失败
            projectListMapper.batchUpdateStatus(projectIds, ProjectConstants.STATUS_FAILED);
        }
    }

    /**
     * 处理单个项目
     * 
     * @param project 项目信息
     */
    private void processIndividualProject(ProjectList project) {
        try {
            log.info("开始处理项目：ID={}, 项目名称={}, 公司名称={}",
                    project.getId(), project.getParsedProjectName(), project.getParsedCompanyName());

            // 模拟任务处理过程 - 使用Thread.sleep(5)替代具体业务逻辑
            Thread.sleep(ProjectConstants.TASK_PROCESS_TIME_MS);

            log.info("项目处理完成：ID={}, 项目名称={}",
                    project.getId(), project.getParsedProjectName());

        } catch (InterruptedException e) {
            log.error("处理项目时被中断：ID={}", project.getId(), e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("项目处理被中断", e);
        } catch (Exception e) {
            log.error("处理项目时发生异常：ID={}", project.getId(), e);
            throw new RuntimeException("项目处理失败", e);
        }
    }

    /**
     * 获取所有项目列表
     * 
     * @return 所有项目列表
     */
    public List<ProjectList> getAllProjects() {
        return projectListMapper.selectAll();
    }
}
package com.pitaya.devdiscovery.projectlist.schedule;

import com.pitaya.devdiscovery.projectlist.service.ProjectListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @program: dev-discovery
 * @ClassName ProjectListSchedule
 * @description: 项目名单定时任务
 * @author: system
 * @date: 2024-12-19
 * @Version 1.0
 **/
@Slf4j
@Component
public class ProjectListSchedule {

    @Autowired
    private ProjectListService projectListService;

    /**
     * 定时处理项目任务
     * 每5分钟执行一次
     * 注意：可以根据需要调整执行频率，当前注释掉以便测试
     */
    // @Scheduled(cron = "0 */5 * * * ?")
    public void processProjectTasksSchedule() {
        log.info("开始执行项目定时任务");
        try {
            int processedTaskCount = projectListService.processProjectTasks();
            String message = "成功处理 " + processedTaskCount + " 个项目任务";
            log.info("项目定时任务执行完成: {}", message);
        } catch (Exception e) {
            log.error("执行项目定时任务失败", e);
        }
    }

    /**
     * 手动触发任务处理（用于测试）
     * 可以通过调用此方法来手动触发任务处理
     */
    public void manualTriggerTask() {
        log.info("手动触发项目任务处理");
        processProjectTasksSchedule();
    }
}
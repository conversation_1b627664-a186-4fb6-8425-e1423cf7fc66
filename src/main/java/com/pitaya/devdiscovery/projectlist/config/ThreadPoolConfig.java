package com.pitaya.devdiscovery.projectlist.config;

import com.pitaya.devdiscovery.projectlist.constant.ProjectConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @program: dev-discovery
 * @ClassName ThreadPoolConfig
 * @description: 线程池配置类
 * @author: system
 * @date: 2024-12-19
 * @Version 1.0
 **/
@Configuration
public class ThreadPoolConfig {

    /**
     * 创建项目任务处理专用线程池
     *
     * @return 线程池执行器
     */
    @Bean("projectTaskExecutor")
    public Executor projectTaskExecutor() {
        return new ThreadPoolExecutor(
                ProjectConstants.CORE_POOL_SIZE, // 核心线程数
                ProjectConstants.MAX_POOL_SIZE, // 最大线程数
                60L, // 线程空闲时间（秒）
                TimeUnit.SECONDS, // 时间单位
                new LinkedBlockingQueue<>(ProjectConstants.QUEUE_CAPACITY), // 工作队列
                r -> {
                    Thread thread = new Thread(r);
                    thread.setName("project-task-" + thread.getId());
                    thread.setDaemon(false);
                    return thread;
                }, // 线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }
}
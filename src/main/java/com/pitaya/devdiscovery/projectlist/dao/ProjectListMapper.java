package com.pitaya.devdiscovery.projectlist.dao;

import com.pitaya.devdiscovery.projectlist.entity.ProjectList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName ProjectListMapper
 * @description: 项目名单数据访问层
 * @author: system
 * @date: 2024-12-19
 * @Version 1.0
 **/
@Mapper
public interface ProjectListMapper {

    /**
     * 根据状态和限制条数查询待处理的项目列表
     *
     * @param status 状态
     * @param limit  限制条数
     * @return 项目列表
     */
    List<ProjectList> selectPendingProjects(@Param("status") String status, @Param("limit") int limit);

    /**
     * 批量更新项目状态
     *
     * @param ids    ID列表
     * @param status 新状态
     * @return 更新条数
     */
    int batchUpdateStatus(@Param("ids") List<Integer> ids, @Param("status") String status);

    /**
     * 根据ID更新项目状态
     *
     * @param id     项目ID
     * @param status 新状态
     * @return 更新条数
     */
    int updateStatusById(@Param("id") Integer id, @Param("status") String status);

    /**
     * 查询所有项目
     *
     * @return 所有项目列表
     */
    List<ProjectList> selectAll();
}
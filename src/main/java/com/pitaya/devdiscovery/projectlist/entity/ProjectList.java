package com.pitaya.devdiscovery.projectlist.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @program: dev-discovery
 * @ClassName ProjectList
 * @description: 项目名单实体类
 * @author: system
 * @date: 2024-12-19
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class ProjectList {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 名单类型
     */
    private String listType;

    /**
     * 项目全名称
     */
    private String fullProjectName;

    /**
     * 解析公司名称
     */
    private String parsedCompanyName;

    /**
     * 解析项目名称
     */
    private String parsedProjectName;

    /**
     * 归属区县
     */
    private String district;

    /**
     * 状态 0：未执行 1：正在执行 2：执行成功 3：执行失败
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
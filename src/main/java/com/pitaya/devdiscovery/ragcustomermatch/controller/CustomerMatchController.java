package com.pitaya.devdiscovery.ragcustomermatch.controller;

import com.pitaya.devdiscovery.common.constant.ApiCodeConstant;
import com.pitaya.devdiscovery.common.dto.ProcessResultDTO;
import com.pitaya.devdiscovery.common.response.BaseResponse;
import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchResult;
import com.pitaya.devdiscovery.ragcustomermatch.service.CustomerMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户匹配控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ragcustomermatch")
public class CustomerMatchController {

    @Autowired
    private CustomerMatchService customerMatchService;

    /**
     * 处理未处理的客户匹配任务
     *
     * @return 处理结果
     */
    @PostMapping("/process")
    public BaseResponse<ProcessResultDTO> processCustomerMatchTasks() {
        log.info("开始处理客户匹配任务");
        try {
            int processedTaskCount = customerMatchService.processCustomerMatchTasks();
            String message = "成功处理 " + processedTaskCount + " 个任务";
            ProcessResultDTO resultDTO = ProcessResultDTO.of(processedTaskCount, message);
            log.info("客户匹配任务处理完成: {}", message);
            return BaseResponse.success(resultDTO);
        } catch (Exception e) {
            log.error("处理客户匹配任务失败", e);
            return BaseResponse.error(ApiCodeConstant.SYSTEM_ERROR, "处理失败：" + e.getMessage());
        }
    }


    /**
     * 获取任务处理结果
     *
     * @param taskId 任务ID
     * @return 处理结果
     */
    @GetMapping("/results/{taskId}")
    public BaseResponse<List<CustomerMatchResult>> getTaskResults(@PathVariable Long taskId) {
        log.info("获取任务处理结果, taskId: {}", taskId);

        try {
            List<CustomerMatchResult> resultList = customerMatchService.getTaskResults(taskId);
            return BaseResponse.success(resultList);
        } catch (Exception e) {
            log.error("获取任务处理结果失败, taskId: {}", taskId, e);
            return BaseResponse.error(ApiCodeConstant.SYSTEM_ERROR, "获取结果失败：" + e.getMessage());
        }
    }
}

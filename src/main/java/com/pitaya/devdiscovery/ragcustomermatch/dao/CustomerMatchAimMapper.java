package com.pitaya.devdiscovery.ragcustomermatch.dao;

import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchAim;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 匹配自然客户等待处理表数据访问接口
 */
@Mapper
public interface CustomerMatchAimMapper {

    /**
     * 根据任务ID查询待处理数据
     *
     * @param taskId 任务ID
     * @return 待处理数据列表
     */
    List<CustomerMatchAim> selectByTaskId(@Param("taskId") Long taskId);
}
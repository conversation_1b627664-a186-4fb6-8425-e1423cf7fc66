package com.pitaya.devdiscovery.ragcustomermatch.dao;

import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 匹配自然客户结果表数据访问接口
 */
@Mapper
public interface CustomerMatchResultMapper {

    /**
     * 批量插入处理结果
     *
     * @param resultList 处理结果列表
     * @return 插入行数
     */
    int batchInsert(@Param("list") List<CustomerMatchResult> resultList);

    /**
     * 根据任务ID查询处理结果
     *
     * @param taskId 任务ID
     * @return 处理结果列表
     */
    List<CustomerMatchResult> selectByTaskId(@Param("taskId") Long taskId);
}
package com.pitaya.devdiscovery.ragcustomermatch.dao;

import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 匹配自然客户任务表数据访问接口
 */
@Mapper
public interface CustomerMatchTaskMapper {

    /**
     * 查询未处理的任务列表
     *
     * @return 未处理的任务列表
     */
    List<CustomerMatchTask> selectUnprocessedTasks();

    /**
     * 更新任务状态
     *
     * @param id     任务ID
     * @param status 任务状态
     * @return 更新行数
     */
    int updateTaskStatus(@Param("id") Long id, @Param("status") String status);
}
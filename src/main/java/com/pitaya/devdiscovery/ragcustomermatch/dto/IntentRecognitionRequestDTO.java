package com.pitaya.devdiscovery.ragcustomermatch.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntentRecognitionRequestDTO {
    private String query;
    private List<String> power;
    private String index;
    private double score;
    private String output;
    @JSONField(name = "search top") // Handle space in JSON key
    private int searchTop;
    @JSONField(name = "top k") // Handle space in JSON key
    private int topK;
}
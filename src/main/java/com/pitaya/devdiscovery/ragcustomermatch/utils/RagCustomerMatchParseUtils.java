package com.pitaya.devdiscovery.ragcustomermatch.utils;

import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.common.entity.CustomerEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class RagCustomerMatchParseUtils {
    private static final Logger logger = LoggerFactory.getLogger(RagCustomerMatchParseUtils.class);
    private static final Pattern PATTERN = Pattern.compile("\\{[^}]*\\}");
    public CustomerEntity parseCustomerContent(String content) {
        try {
            Matcher matcher = PATTERN.matcher(content);
            if (matcher.find()) {
                String jsonStr = matcher.group(0);
                try {
                    jsonStr = jsonStr.replaceAll("nan", "null");
                    JSONObject obj = JSONObject.parseObject(jsonStr);
                    CustomerEntity entity = new CustomerEntity();
                    entity.setEparchyName(StringUtils.isEmpty(obj.getString("地市"))?"":obj.getString("地市"));
                    entity.setCustomerId(obj.getString("自然客户ID"));
                    entity.setCustomerName(obj.getString("自然客户名称"));
                    return entity;
                }catch (Exception e){
                    logger.info("parseCustomerContent error,jsonStr:{}",jsonStr);
                    return null;
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<CustomerEntity> parseCustomerContentList(List<String> contentList) {
        List<CustomerEntity> result = new ArrayList<>();
        if (contentList == null || contentList.isEmpty()) {
            return result;
        }
        for (String content : contentList) {
            CustomerEntity entity = parseCustomerContent(content);
            if (entity != null) {
                result.add(entity);
            }
        }
        return result;
    }
}

package com.pitaya.devdiscovery.ragcustomermatch.schedule;

import com.pitaya.devdiscovery.ragcustomermatch.service.CustomerMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: dev-discovery
 * @ClassName RagCustomerMatchSchedule
 * @description:
 * @author: majian
 * @date: 2025-05-12 18:11
 * @Version 1.0
 **/
@Slf4j
@Component
public class RagCustomerMatchSchedule {
    @Autowired
    private CustomerMatchService customerMatchService;
    //@Scheduled(cron = "0/5 * * * * ?")
    public void processCustomerMatchTasksSchedule() {
        log.info("开始处理客户匹配任务");
        try {
            int processedTaskCount = customerMatchService.processCustomerMatchTasks();
            String message = "成功处理 " + processedTaskCount + " 个任务";
            log.info("客户匹配任务处理完成: {}", message);
        } catch (Exception e) {
            log.error("处理客户匹配任务失败", e);
        }
    }
}

package com.pitaya.devdiscovery.ragcustomermatch.entity;

import lombok.Data;

/**
 * 匹配自然客户结果表实体类
 */
@Data
public class CustomerMatchResult {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 地市名称
     */
    private String eparchyName;

    /**
     * 自然客户查询条件
     */
    private String customerNameQuery;

    /**
     * 匹配出自然客户地市
     */
    private String customerEparchyName;

    /**
     * 匹配出自然客户ID
     */
    private String customerId;

    /**
     * 匹配出自然客户名称
     */
    private String customerName;
}
package com.pitaya.devdiscovery.ragcustomermatch.service;

import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchAim;
import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchResult;

import java.util.List;

/**
 * 客户匹配数据处理服务接口
 */
public interface CustomerMatchProcessorService {

    /**
     * 处理客户匹配数据
     *
     * @param aimList 待处理的客户数据列表
     * @return 处理后的结果列表
     */
    List<CustomerMatchResult> processCustomerMatchData(List<CustomerMatchAim> aimList);
}
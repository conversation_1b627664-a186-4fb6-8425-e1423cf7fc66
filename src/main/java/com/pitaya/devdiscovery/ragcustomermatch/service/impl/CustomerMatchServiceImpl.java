package com.pitaya.devdiscovery.ragcustomermatch.service.impl;

import com.pitaya.devdiscovery.ragcustomermatch.constant.TaskStatusConstant;
import com.pitaya.devdiscovery.ragcustomermatch.dao.CustomerMatchAimMapper;
import com.pitaya.devdiscovery.ragcustomermatch.dao.CustomerMatchResultMapper;
import com.pitaya.devdiscovery.ragcustomermatch.dao.CustomerMatchTaskMapper;
import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchAim;
import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchResult;
import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchTask;
import com.pitaya.devdiscovery.ragcustomermatch.service.CustomerMatchProcessorService;
import com.pitaya.devdiscovery.ragcustomermatch.service.CustomerMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 客户匹配服务实现类
 */
@Slf4j
@Service
public class CustomerMatchServiceImpl implements CustomerMatchService {

    @Autowired
    private CustomerMatchTaskMapper taskMapper;

    @Autowired
    private CustomerMatchAimMapper aimMapper;

    @Autowired
    private CustomerMatchResultMapper resultMapper;

    @Autowired
    private CustomerMatchProcessorService processorService;

    /**
     * 处理未处理的客户匹配任务
     *
     * @return 处理的任务数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int processCustomerMatchTasks() {
        List<CustomerMatchTask> unprocessedTasks = taskMapper.selectUnprocessedTasks();
        if (CollectionUtils.isEmpty(unprocessedTasks)) {
            log.info("没有未处理的客户匹配任务");
            return 0;
        }
        int processedTaskCount = 0;
        for (CustomerMatchTask task : unprocessedTasks) {
            try {
                processTask(task);
                processedTaskCount++;
            } catch (Exception e) {
                log.error("处理任务失败, taskId: {}", task.getId(), e);
                taskMapper.updateTaskStatus(task.getId(), TaskStatusConstant.STATUS_FAILED);
            }
        }
        log.info("成功处理客户匹配任务 {} 个", processedTaskCount);
        return processedTaskCount;
    }

    /**
     * 获取任务处理结果
     *
     * @param taskId 任务ID
     * @return 处理结果列表
     */
    @Override
    public List<CustomerMatchResult> getTaskResults(Long taskId) {
        log.info("获取任务处理结果, taskId: {}", taskId);
        return resultMapper.selectByTaskId(taskId);
    }

    /**
     * 处理单个任务
     *
     * @param task 待处理任务
     */
    private void processTask(CustomerMatchTask task) {
        Long taskId = task.getId();
        List<CustomerMatchAim> aimList = aimMapper.selectByTaskId(taskId);
        if (CollectionUtils.isEmpty(aimList)) {
            log.info("任务 {} 没有待处理数据", taskId);
            taskMapper.updateTaskStatus(taskId, TaskStatusConstant.STATUS_PROCESSED);
            return;
        }
        List<CustomerMatchResult> resultList = processorService.processCustomerMatchData(aimList);
        if (!CollectionUtils.isEmpty(resultList)) {
            resultMapper.batchInsert(resultList);
        }
        taskMapper.updateTaskStatus(taskId, TaskStatusConstant.STATUS_PROCESSED);
        log.info("任务 {} 处理完成，处理数据 {} 条", taskId, aimList.size());
    }
}

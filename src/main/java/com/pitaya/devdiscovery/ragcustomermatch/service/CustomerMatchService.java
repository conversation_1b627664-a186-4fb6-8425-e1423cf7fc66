package com.pitaya.devdiscovery.ragcustomermatch.service;

import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchResult;

import java.util.List;

/**
 * 客户匹配服务接口
 */
public interface CustomerMatchService {

    /**
     * 处理未处理的客户匹配任务
     * 
     * @return 处理的任务数量
     */
    int processCustomerMatchTasks();

    /**
     * 获取任务处理结果
     *
     * @param taskId 任务ID
     * @return 处理结果列表
     */
    List<CustomerMatchResult> getTaskResults(Long taskId);
}
package com.pitaya.devdiscovery.ragcustomermatch.service.impl;


import com.pitaya.devdiscovery.aiability.constant.RagKnowledgeConstant;
import com.pitaya.devdiscovery.aiability.utils.InternalRagUtils;
import com.pitaya.devdiscovery.aiability.utils.TextSimilarityUtils;
import com.pitaya.devdiscovery.common.entity.CustomerEntity;
import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchAim;
import com.pitaya.devdiscovery.ragcustomermatch.entity.CustomerMatchResult;
import com.pitaya.devdiscovery.ragcustomermatch.service.CustomerMatchProcessorService;
import com.pitaya.devdiscovery.ragcustomermatch.utils.RagCustomerMatchParseUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CustomerMatchProcessorServiceImpl implements CustomerMatchProcessorService {
    private static final Logger logger = LoggerFactory.getLogger(CustomerMatchProcessorServiceImpl.class);
    @Autowired
    private InternalRagUtils internalRagUtils;
    @Autowired
    private RagCustomerMatchParseUtils ragCustomerMatchParseUtils;
    @Override
    public List<CustomerMatchResult> processCustomerMatchData(List<CustomerMatchAim> aimList) {
        List<CustomerMatchResult> resultList = new ArrayList<>();
        for (CustomerMatchAim aim : aimList) {
            String query = new StringBuffer("查询").append(aim.getCustomerNameQuery().contains(aim.getEparchyName())?"":(aim.getEparchyName()+"市")).append(aim.getCustomerNameQuery()).append("自然客户信息").toString();
            logger.info("query: {}",query);
            if (!aim.getCustomerNameQuery().contains("菏泽市立医院")){
                continue;
            }
            String response = internalRagUtils.callRagApi(RagKnowledgeConstant.PRODUCT_RECOMMEND_KNOWID, 10, query);
            CustomerMatchResult result = new CustomerMatchResult();
            result.setTaskId(aim.getTaskId());
            result.setEparchyName(aim.getEparchyName());
            result.setCustomerNameQuery(aim.getCustomerNameQuery());
            if(internalRagUtils.hasValidRagResponse(response)){
                List<String> contentList = internalRagUtils.parseRagResponseContents(response, "自然客户清单");
                List<CustomerEntity> customerEntityList = ragCustomerMatchParseUtils.parseCustomerContentList(contentList);
                if(customerEntityList != null && !customerEntityList.isEmpty()){
                    CustomerEntity entity = preciseMatch(aim, customerEntityList);
                    result.setCustomerEparchyName(entity.getEparchyName());
                    result.setCustomerId(entity.getCustomerId());
                    result.setCustomerName(entity.getCustomerName());
                }
            }else {
                logger.info("rag response is invalid,query: {}",query);
            }
            resultList.add(result);
        }
        return resultList;
    }
    private CustomerEntity preciseMatch(CustomerMatchAim queryAim, List<CustomerEntity> customerEntityList){
        String aimEparchyName = queryAim.getEparchyName().replaceAll("市","");
        String aimCustomerName = queryAim.getCustomerNameQuery();
        String aimEparchyCustomerName = new StringBuffer(aimCustomerName.contains(aimEparchyName)?"":(aimEparchyName+"市")).append(aimCustomerName).toString();
        for(CustomerEntity item : customerEntityList){
            if(aimEparchyName.equals(item.getEparchyName()) && (aimCustomerName.equals(item.getCustomerName())||aimEparchyCustomerName.equals(item.getCustomerName()))){
                return item;
            }
        }
        for(CustomerEntity item : customerEntityList){
            String customerName = item.getCustomerName();
            if(aimEparchyName.equals(item.getEparchyName()) && (aimCustomerName.contains(customerName)|| customerName.contains(aimCustomerName))){
                return item;
            }
        }
        for(CustomerEntity item : customerEntityList){
            String customerName = item.getCustomerName();
            if(StringUtils.isEmpty(item.getEparchyName()) && (aimCustomerName.contains(customerName)|| customerName.contains(aimCustomerName))){
                return item;
            }
        }
        for (CustomerEntity item : customerEntityList){
            item.setSimilarity(TextSimilarityUtils.similarity(aimCustomerName, item.getCustomerName()));
        }
        if(StringUtils.isNotEmpty(aimEparchyName)){
            customerEntityList = customerEntityList.stream().filter(item -> StringUtils.isEmpty(item.getEparchyName())||item.getEparchyName().equals(aimEparchyName)).collect(Collectors.toList());
        }
        List<CustomerEntity> sortedList = customerEntityList.stream().filter(item -> item.getSimilarity() > 0.0).sorted(Comparator.comparingDouble(CustomerEntity::getSimilarity).reversed()).collect(Collectors.toList());
        if(sortedList != null && !sortedList.isEmpty()){
            logger.info("目标: 地市：{} 查询：{}",queryAim.getEparchyName(),queryAim.getCustomerNameQuery());
            sortedList.stream().forEach(item -> {
                logger.info("匹配: 地市：{} 名称：{} 相似度：{}",item.getEparchyName(),item.getCustomerName(),item.getSimilarity());
            });
        }
        return sortedList.get(0);
    }
}

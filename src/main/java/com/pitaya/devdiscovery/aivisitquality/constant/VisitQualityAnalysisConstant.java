package com.pitaya.devdiscovery.aivisitquality.constant;

/**
 * <AUTHOR>
 */
public class VisitQualityAnalysisConstant {
    /**
     * 拜访质量分析智能体Flow ID
     */
    public static final String VISIT_QUALITY_ANALYSIS_FLOW_ID = "f32b904a96a2459fab500a1d00eebb91";
    
    /**
     * 处理状态常量
     */
    public static final String STATUS_PENDING = "0";      // 待处理
    public static final String STATUS_PROCESSING = "1";   // 正在处理
    public static final String STATUS_PROCESSED = "2";    // 已处理
    public static final String STATUS_FAILED = "3";       // 处理失败
    
    /**
     * 评分维度常量
     */
    public static final String DIMENSION_BUSINESS = "业务成果";
    public static final String DIMENSION_PROCESS = "过程价值";
    public static final String DIMENSION_LEVEL = "拜访层级";
    public static final String DIMENSION_CONTENT = "内容质量";
    public static final String DIMENSION_DEDUCT = "特别扣分项";
}

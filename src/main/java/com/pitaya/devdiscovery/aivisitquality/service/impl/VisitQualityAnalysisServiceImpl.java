package com.pitaya.devdiscovery.aivisitquality.service.impl;

import com.pitaya.devdiscovery.aivisitquality.dao.VisitQualityAnalysisMapper;
import com.pitaya.devdiscovery.aivisitquality.dto.VisitQualityAnalysisDTO;
import com.pitaya.devdiscovery.aivisitquality.entity.VisitQualityAnalysis;
import com.pitaya.devdiscovery.aivisitquality.service.IVisitQualityAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 拜访质量分析服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Service
public class VisitQualityAnalysisServiceImpl implements IVisitQualityAnalysisService {

    @Autowired
    private VisitQualityAnalysisMapper visitQualityAnalysisMapper;

    @Override
    public List<VisitQualityAnalysis> selectPendingVisitQualityAnalysis(VisitQualityAnalysisDTO param) {
        return visitQualityAnalysisMapper.selectPendingVisitQualityAnalysis(param);
    }

    @Override
    public int updateVisitQualityAnalysis(VisitQualityAnalysisDTO param) {
        return visitQualityAnalysisMapper.updateVisitQualityAnalysis(param);
    }

    @Override
    public int insertVisitQualityAnalysis(VisitQualityAnalysis visitQualityAnalysis) {
        return visitQualityAnalysisMapper.insertVisitQualityAnalysis(visitQualityAnalysis);
    }

    @Override
    public VisitQualityAnalysis selectById(Long id) {
        return visitQualityAnalysisMapper.selectById(id);
    }

    @Override
    public VisitQualityAnalysis selectByRecordId(String recordId) {
        return visitQualityAnalysisMapper.selectByRecordId(recordId);
    }
}

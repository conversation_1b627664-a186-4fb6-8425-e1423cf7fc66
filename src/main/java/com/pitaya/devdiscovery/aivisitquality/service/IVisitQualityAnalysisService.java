package com.pitaya.devdiscovery.aivisitquality.service;

import com.pitaya.devdiscovery.aivisitquality.dto.VisitQualityAnalysisDTO;
import com.pitaya.devdiscovery.aivisitquality.entity.VisitQualityAnalysis;

import java.util.List;


public interface IVisitQualityAnalysisService {
    /**
     * 查询待处理的拜访质量分析任务列表
     * @param param 查询参数
     * @return 待处理任务列表
     */
    List<VisitQualityAnalysis> selectPendingVisitQualityAnalysis(VisitQualityAnalysisDTO param);
    
    /**
     * 更新拜访质量分析结果
     * @param param 更新参数
     * @return 更新结果
     */
    int updateVisitQualityAnalysis(VisitQualityAnalysisDTO param);
    
    /**
     * 插入拜访质量分析记录
     * @param visitQualityAnalysis 拜访质量分析实体
     * @return 插入结果
     */
    int insertVisitQualityAnalysis(VisitQualityAnalysis visitQualityAnalysis);
    
    /**
     * 根据ID查询拜访质量分析记录
     * @param id 主键ID
     * @return 拜访质量分析记录
     */
    VisitQualityAnalysis selectById(Long id);
    
    /**
     * 根据记录ID查询拜访质量分析记录
     * @param recordId 拜访记录ID
     * @return 拜访质量分析记录
     */
    VisitQualityAnalysis selectByRecordId(String recordId);
}

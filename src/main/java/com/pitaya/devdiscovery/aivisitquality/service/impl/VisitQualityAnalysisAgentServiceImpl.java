package com.pitaya.devdiscovery.aivisitquality.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.aiability.config.AIThreadPoolConfig;
import com.pitaya.devdiscovery.aivisitquality.constant.VisitQualityAnalysisConstant;
import com.pitaya.devdiscovery.aivisitquality.dto.VisitQualityAnalysisDTO;
import com.pitaya.devdiscovery.aivisitquality.entity.VisitQualityAnalysis;
import com.pitaya.devdiscovery.aivisitquality.service.IVisitQualityAnalysisAgentService;
import com.pitaya.devdiscovery.aivisitquality.service.IVisitQualityAnalysisService;
import com.pitaya.devdiscovery.bishengagent.service.BishengAgentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;


@Service
public class VisitQualityAnalysisAgentServiceImpl implements IVisitQualityAnalysisAgentService {
    private static final Logger logger = LoggerFactory.getLogger(VisitQualityAnalysisAgentServiceImpl.class);
    
    @Autowired
    private BishengAgentService bishengAgentService;
    @Autowired
    private IVisitQualityAnalysisService visitQualityAnalysisService;
    
    /**
     * 处理拜访质量分析任务
     */
    @Override
    @Async(AIThreadPoolConfig.TASK_EXECUTOR_NAME)
    public void handleVisitQualityAnalysis(VisitQualityAnalysis visitQualityAnalysis) {
        try {
            logger.info("开始处理拜访质量分析任务，ID: {}", visitQualityAnalysis.getId());
            
            // 先更新状态为"正在处理"
            VisitQualityAnalysisDTO processingDto = new VisitQualityAnalysisDTO();
            processingDto.setId(visitQualityAnalysis.getId());
            processingDto.setStatus(VisitQualityAnalysisConstant.STATUS_PROCESSING);
            visitQualityAnalysisService.updateVisitQualityAnalysis(processingDto);
            logger.info("已更新任务状态为正在处理，ID: {}", visitQualityAnalysis.getId());
            
            // 构建请求参数
            JSONObject request = new JSONObject();
            request.put("visit_content", visitQualityAnalysis.getVisitContent());
            String query = JSONObject.toJSONString(request);
            logger.info("调用智能体，请求参数: {}", query);
            
            // 调用智能体
            JSONObject response = invokeVisitQualityAnalysisAgent(query);
            logger.info("智能体返回结果: {}", response.toJSONString());
            
            // 解析结果
            String totalScore = response.getString("total_score");
            String finalSummary = response.getString("final_summary");
            JSONArray scoreDetailsArray = response.getJSONArray("score_details");
            
            // 解析各维度评分
            String businessScore = "", businessReason = "";
            String processScore = "", processReason = "";
            String levelScore = "", levelReason = "";
            String contentScore = "", contentReason = "";
            String deductScore = "", deductReason = "";
            
            if (scoreDetailsArray != null) {
                for (int i = 0; i < scoreDetailsArray.size(); i++) {
                    JSONObject detail = scoreDetailsArray.getJSONObject(i);
                    String dimension = detail.getString("dimension");
                    String score = detail.getString("score");
                    String reason = detail.getString("reason");
                    
                    switch (dimension) {
                        case VisitQualityAnalysisConstant.DIMENSION_BUSINESS:
                            businessScore = score;
                            businessReason = reason;
                            break;
                        case VisitQualityAnalysisConstant.DIMENSION_PROCESS:
                            processScore = score;
                            processReason = reason;
                            break;
                        case VisitQualityAnalysisConstant.DIMENSION_LEVEL:
                            levelScore = score;
                            levelReason = reason;
                            break;
                        case VisitQualityAnalysisConstant.DIMENSION_CONTENT:
                            contentScore = score;
                            contentReason = reason;
                            break;
                        case VisitQualityAnalysisConstant.DIMENSION_DEDUCT:
                            deductScore = score;
                            deductReason = reason;
                            break;
                    }
                }
            }
            
            // 更新分析结果
            VisitQualityAnalysisDTO resultDto = new VisitQualityAnalysisDTO();
            resultDto.setId(visitQualityAnalysis.getId());
            resultDto.setStatus(VisitQualityAnalysisConstant.STATUS_PROCESSED);
            resultDto.setTotalScore(totalScore);
            resultDto.setFinalSummary(finalSummary);
            resultDto.setScoreDetails(JSONObject.toJSONString(scoreDetailsArray));
            resultDto.setBusinessScore(businessScore);
            resultDto.setBusinessReason(businessReason);
            resultDto.setProcessScore(processScore);
            resultDto.setProcessReason(processReason);
            resultDto.setLevelScore(levelScore);
            resultDto.setLevelReason(levelReason);
            resultDto.setContentScore(contentScore);
            resultDto.setContentReason(contentReason);
            resultDto.setDeductScore(deductScore);
            resultDto.setDeductReason(deductReason);
            
            visitQualityAnalysisService.updateVisitQualityAnalysis(resultDto);
            logger.info("拜访质量分析任务处理完成，ID: {}", visitQualityAnalysis.getId());
            
        } catch (Exception e) {
            logger.error("拜访质量分析任务处理失败，ID: {}", visitQualityAnalysis.getId(), e);
            
            // 更新状态为处理失败
            VisitQualityAnalysisDTO failedDto = new VisitQualityAnalysisDTO();
            failedDto.setId(visitQualityAnalysis.getId());
            failedDto.setStatus(VisitQualityAnalysisConstant.STATUS_FAILED);
            visitQualityAnalysisService.updateVisitQualityAnalysis(failedDto);
        }
    }
    
    /**
     * 调用拜访质量分析智能体
     *
     * @param query 查询参数
     * @return 智能体返回结果
     */
    private JSONObject invokeVisitQualityAnalysisAgent(String query) {
        Map<String, Object> result = bishengAgentService.invokeWorkflowV2(VisitQualityAnalysisConstant.VISIT_QUALITY_ANALYSIS_FLOW_ID, query);
        String content = (String) result.get("content");
        JSONObject contentJSON = JSONObject.parseObject(content);
        Set<String> keys = contentJSON.keySet();
        JSONObject analysisResult = new JSONObject();
        for (String key : keys) {
            String str = contentJSON.getString(key);
            JSONObject obj = JSONObject.parseObject(str);
            analysisResult.putAll(obj);
        }
        return analysisResult;
    }
}

package com.pitaya.devdiscovery.aivisitquality.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class VisitQualityAnalysisDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 拜访记录ID，业务主键
     */
    private String recordId;

    /**
     * 拜访客户名称
     */
    private String customName;

    /**
     * 拜访内容，智能体分析的原始输入
     */
    private String visitContent;

    /**
     * 分析状态：0-待处理，1-正在处理，2-已处理，3-处理失败
     */
    private String status;

    /**
     * 总分
     */
    private String totalScore;

    /**
     * 最终总结，智能体生成的综合评价
     */
    private String finalSummary;

    /**
     * 评分详情，完整的JSON格式评分信息
     */
    private String scoreDetails;

    /**
     * 业务成果得分
     */
    private String businessScore;

    /**
     * 业务成果评分原因
     */
    private String businessReason;

    /**
     * 过程价值得分
     */
    private String processScore;

    /**
     * 过程价值评分原因
     */
    private String processReason;

    /**
     * 拜访层级得分
     */
    private String levelScore;

    /**
     * 拜访层级评分原因
     */
    private String levelReason;

    /**
     * 内容质量得分
     */
    private String contentScore;

    /**
     * 内容质量评分原因
     */
    private String contentReason;

    /**
     * 特别扣分项得分
     */
    private String deductScore;

    /**
     * 特别扣分项原因
     */
    private String deductReason;
}

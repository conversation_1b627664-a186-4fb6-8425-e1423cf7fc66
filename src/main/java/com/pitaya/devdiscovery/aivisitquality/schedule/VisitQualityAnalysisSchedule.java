package com.pitaya.devdiscovery.aivisitquality.schedule;

import com.pitaya.devdiscovery.aivisitquality.dto.VisitQualityAnalysisDTO;
import com.pitaya.devdiscovery.aivisitquality.entity.VisitQualityAnalysis;
import com.pitaya.devdiscovery.aivisitquality.service.IVisitQualityAnalysisAgentService;
import com.pitaya.devdiscovery.aivisitquality.service.IVisitQualityAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class VisitQualityAnalysisSchedule {
    @Autowired
    private IVisitQualityAnalysisService visitQualityAnalysisService;
    @Autowired
    private IVisitQualityAnalysisAgentService visitQualityAnalysisAgentService;
    /**
     * 定时执行拜访质量分析任务
     */
    public void analyzeVisitQualityByAI() {
        log.info("开始拜访质量分析任务...");
        try {
            VisitQualityAnalysisDTO param = new VisitQualityAnalysisDTO();
            List<VisitQualityAnalysis> pendingTasks = visitQualityAnalysisService.selectPendingVisitQualityAnalysis(param);
            if (pendingTasks.isEmpty()) {
                log.info("没有待处理的拜访质量分析任务");
                return;
            }
            log.info("查询到待处理任务数量: {}", pendingTasks.size());
            for (VisitQualityAnalysis task : pendingTasks) {
                log.info("处理任务: ID={}, 记录ID={}, 客户名称={}", task.getId(), task.getRecordId(), task.getCustomName());
                visitQualityAnalysisAgentService.handleVisitQualityAnalysis(task);
            }
            log.info("拜访质量分析任务执行完成");
        } catch (Exception e) {
            log.error("拜访质量分析任务执行失败", e);
        }
    }
}

package com.pitaya.devdiscovery.aivisitquality.controller;

import com.pitaya.devdiscovery.aivisitquality.dto.VisitQualityAnalysisDTO;
import com.pitaya.devdiscovery.aivisitquality.entity.VisitQualityAnalysis;
import com.pitaya.devdiscovery.aivisitquality.service.IVisitQualityAnalysisAgentService;
import com.pitaya.devdiscovery.aivisitquality.service.IVisitQualityAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/visitQualityAnalysisTest")
public class VisitQualityAnalysisTestController {
    private static final Logger logger = LoggerFactory.getLogger(VisitQualityAnalysisTestController.class);
    
    @Autowired
    private IVisitQualityAnalysisService visitQualityAnalysisService;
    @Autowired
    private IVisitQualityAnalysisAgentService visitQualityAnalysisAgentService;
    
    @RequestMapping("/analyzeVisitQualityByAI")
    public void analyzeVisitQualityByAI() {
        logger.info("开始执行拜访质量分析任务...");
        try {
            VisitQualityAnalysisDTO param = new VisitQualityAnalysisDTO();
            List<VisitQualityAnalysis> pendingTasks = visitQualityAnalysisService.selectPendingVisitQualityAnalysis(param);
            logger.info("查询到待处理任务数量: {}", pendingTasks.size());
            for (VisitQualityAnalysis task : pendingTasks) {
                logger.info("处理任务: ID={}, 记录ID={}, 客户名称={}", task.getId(), task.getRecordId(), task.getCustomName());
                visitQualityAnalysisAgentService.handleVisitQualityAnalysis(task);
            }
            logger.info("拜访质量分析任务执行完成");
        } catch (Exception e) {
            logger.error("拜访质量分析任务执行失败", e);
        }
    }
}

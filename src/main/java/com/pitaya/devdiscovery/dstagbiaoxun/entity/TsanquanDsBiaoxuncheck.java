package com.pitaya.devdiscovery.dstagbiaoxun.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: dev-discovery
 * @ClassName TsanquanDsBiaoxuncheck
 * @description:
 * @author: ma<PERSON>an
 * @date: 2025-05-12 16:19
 * @Version 1.0
 **/
@Data
public class TsanquanDsBiaoxuncheck {
    private Integer id;
    private String naturalCustomerId;
    private String naturalCustomerName;
    private String biddingTitle;
    private String biddingContent;
    private String releaseTime;
    private String bidWinner;
    private String quantity;
    private String city;
    private String projectNumber;
    private String projectName;
    private String status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

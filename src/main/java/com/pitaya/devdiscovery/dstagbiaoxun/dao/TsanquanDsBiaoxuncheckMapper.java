package com.pitaya.devdiscovery.dstagbiaoxun.dao;

import com.pitaya.devdiscovery.dstagbiaoxun.entity.TsanquanDsBiaoxuncheck;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @program: dev-discovery
 * @ClassName TsanquanDsBiaoxuncheckMapper
 * @description:
 * @author: ma<PERSON>an
 * @date: 2025-05-12 16:20
 * @Version 1.0
 **/
@Mapper
public interface TsanquanDsBiaoxuncheckMapper {
    // 根据natural_customer_id查询
    TsanquanDsBiaoxuncheck selectById(@Param("naturalCustomerId") String naturalCustomerId);

    // 根据status查询列表
    List<TsanquanDsBiaoxuncheck> selectByStatus(@Param("status") String status);

    // 批量插入
    int batchInsert(@Param("list") List<TsanquanDsBiaoxuncheck> list);

    // 批量更新status
    int batchUpdateStatus(@Param("ids") List<Integer> ids, @Param("status") String status);

    // 根据id更新status
    int updateStatusById(@Param("naturalCustomerId") String naturalCustomerId, @Param("status") String status);
}

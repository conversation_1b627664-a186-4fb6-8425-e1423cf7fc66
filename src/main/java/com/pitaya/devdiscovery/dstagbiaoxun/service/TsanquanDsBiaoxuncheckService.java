package com.pitaya.devdiscovery.dstagbiaoxun.service;

import com.pitaya.devdiscovery.dstagbiaoxun.entity.TsanquanDsBiaoxuncheck;
import java.util.List;

public interface TsanquanDsBiaoxuncheckService {

    /**
     * 根据natural_customer_id查询
     *
     * @param naturalCustomerId 客户ID
     * @return 标讯检查信息
     */
    TsanquanDsBiaoxuncheck getById(String naturalCustomerId);

    /**
     * 根据status查询列表
     *
     * @param status 状态
     * @return 标讯检查信息列表
     */
    List<TsanquanDsBiaoxuncheck> getByStatus(String status);

    /**
     * 批量插入
     *
     * @param list 标讯检查信息列表
     * @return 插入成功的记录数
     */
    int batchInsert(List<TsanquanDsBiaoxuncheck> list);

    /**
     * 批量更新status
     *
     * @param ids 数据ID列表
     * @param status             状态
     * @return 更新成功的记录数
     */
    int batchUpdateStatus(List<Integer> ids, String status);

    /**
     * 根据id更新status
     *
     * @param naturalCustomerId 客户ID
     * @param status            状态
     * @return 更新成功的记录数
     */
    int updateStatusById(String naturalCustomerId, String status);

    /**
     * 从Excel导入数据
     *
     * @return 导入成功的记录数
     */
    int importFromExcel();

    /**
     * 处理待处理的标讯数据，调用DeepSeek API判断是否为数字化项目
     *
     * @return 处理成功的记录数
     */
    int processData();
}

package com.pitaya.devdiscovery.dstagbiaoxun.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pitaya.devdiscovery.aiability.utils.InternalDeepSeekUtils;
import com.pitaya.devdiscovery.common.utils.EasyExcelUtils;
import com.pitaya.devdiscovery.dstagbiaoxun.dao.TsanquanDsBiaoxuncheckMapper;
import com.pitaya.devdiscovery.dstagbiaoxun.dto.BiaoxunExcelDTO;
import com.pitaya.devdiscovery.dstagbiaoxun.entity.TsanquanDsBiaoxuncheck;
import com.pitaya.devdiscovery.dstagbiaoxun.service.TsanquanDsBiaoxuncheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @program: dev-discovery
 * @ClassName TsanquanDsBiaoxuncheckServiceImpl
 * @description: 标讯检查服务实现类
 * @author: majian
 * @date: 2025-05-12 16:30
 * @Version 1.0
 **/
@Service
@Slf4j
public class TsanquanDsBiaoxuncheckServiceImpl implements TsanquanDsBiaoxuncheckService {

    @Autowired
    private TsanquanDsBiaoxuncheckMapper tsanquanDsBiaoxuncheckMapper;
    @Autowired
    private InternalDeepSeekUtils internalDeepSeekUtils;

    // Excel文件路径，该路径需要根据实际情况修改
    private static final String EXCEL_FILE_PATH = "C:\\Users\\<USER>\\Desktop\\商机&标讯打标数据\\标讯数据1.xlsx";

    @Override
    public TsanquanDsBiaoxuncheck getById(String naturalCustomerId) {
        log.info("根据客户ID查询标讯检查信息, naturalCustomerId={}", naturalCustomerId);
        return tsanquanDsBiaoxuncheckMapper.selectById(naturalCustomerId);
    }

    @Override
    public List<TsanquanDsBiaoxuncheck> getByStatus(String status) {
        log.info("查询状态为{}的标讯检查信息列表", status);
        return tsanquanDsBiaoxuncheckMapper.selectByStatus(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<TsanquanDsBiaoxuncheck> list) {
        if (list == null || list.isEmpty()) {
            log.warn("批量插入数据为空，无需执行");
            return 0;
        }
        log.info("批量插入标讯检查信息, 数量={}", list.size());
        return tsanquanDsBiaoxuncheckMapper.batchInsert(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<Integer> ids, String status) {
        log.info("批量更新标讯检查状态, 客户数量={}, 目标状态={}", ids.size(), status);
        return tsanquanDsBiaoxuncheckMapper.batchUpdateStatus(ids, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatusById(String naturalCustomerId, String status) {
        log.info("更新客户ID={}的标讯检查状态为{}", naturalCustomerId, status);
        return tsanquanDsBiaoxuncheckMapper.updateStatusById(naturalCustomerId, status);
    }

    /**
     * 将Excel数据转换为实体对象
     *
     * @param dtoList Excel数据列表
     * @return 实体对象列表
     */
    private List<TsanquanDsBiaoxuncheck> convertToEntities(List<BiaoxunExcelDTO> dtoList) {
        List<TsanquanDsBiaoxuncheck> entityList = new ArrayList<>(dtoList.size());
        for (BiaoxunExcelDTO dto : dtoList) {
            TsanquanDsBiaoxuncheck entity = new TsanquanDsBiaoxuncheck();
            entity.setNaturalCustomerId(dto.getNaturalCustomerId());
            entity.setNaturalCustomerName(dto.getNaturalCustomerName());
            entity.setBiddingTitle(dto.getBiddingTitle());
            entity.setBiddingContent(dto.getBiddingContent());
            entity.setReleaseTime(dto.getReleaseTimeStr());
            entity.setBidWinner(dto.getBidWinner());
            entity.setQuantity(dto.getQuantityStr());
            entity.setCity(dto.getCity());
            entity.setProjectNumber(dto.getProjectNumber());
            entity.setProjectName(dto.getProjectName());
            entity.setStatus("PENDING");
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            entity.setCreateTime(now);
            entity.setUpdateTime(now);

            entityList.add(entity);
        }

        return entityList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importFromExcel() {
        log.info("开始从Excel导入标讯检查数据, 文件路径: {}", EXCEL_FILE_PATH);
        AtomicInteger totalCount = new AtomicInteger(0);
        int start = 300;
        int end = 400;
        final int[] cursor = {0};
        try {
            EasyExcelUtils.readExcel(EXCEL_FILE_PATH, BiaoxunExcelDTO.class, dataList -> {
                cursor[0] = cursor[0] +1;
                if(cursor[0] >= start && cursor[0] < end){
                    List<TsanquanDsBiaoxuncheck> entityList = convertToEntities(dataList);
                    if (!entityList.isEmpty()) {
                        int inserted = batchInsert(entityList);
                        totalCount.addAndGet(inserted);
                        log.info("成功导入{}条数据", inserted);
                    }
                }else {
                    log.info("跳过第{}条数据", cursor[0]);
                }
            });
            log.info("Excel导入完成，共导入{}条数据", totalCount.get());
            return totalCount.get();
        } catch (Exception e) {
            log.error("Excel导入失败", e);
            throw new RuntimeException("Excel导入失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int processData() {
        List<TsanquanDsBiaoxuncheck> dataList = tsanquanDsBiaoxuncheckMapper.selectByStatus("PENDING");
        int length = dataList.size();
        int batchSize = 20;
        int processedCount = 0;
        JSONArray array = new JSONArray();
        Map<Integer, TsanquanDsBiaoxuncheck> dataMap = new HashMap<>();
        for (int i = 0; i < length; i++) {
            TsanquanDsBiaoxuncheck item = dataList.get(i);
            JSONObject object = new JSONObject();
            object.put("数据ID", item.getId());
            object.put("自然客户名称", item.getNaturalCustomerName());
            object.put("标讯标题", item.getBiddingTitle());
            object.put("标讯内容", item.getBiddingContent());
            object.put("项目名称", item.getProjectName());
            array.add(object);
            if (i != 0 && (i % batchSize == 0 || i == length - 1)) {
                StringBuffer querySb = new StringBuffer("请根据标讯标题、标讯内容、项目名称判断标讯信息是否为数字化项目，返回结果为json，key为数据ID，value为是或否");
                querySb.append(JSONArray.toJSONString(array));
                try {
                    log.info("DeepSeek API 请求参数: {}", querySb);
                    String answer = internalDeepSeekUtils.ask(querySb.toString());
                    log.info("DeepSeek API 返回结果: {}", answer);
                    // 使用正则表达式提取JSON内容
                    String jsonStr = extractJsonFromResponse(answer);
                    log.info("提取的JSON数据: {}", jsonStr);
                    JSONObject resultJson = JSONObject.parseObject(jsonStr);
                    List<Integer> digitalIds = new ArrayList<>();
                    List<Integer> nonDigitalIds = new ArrayList<>();
                    for (Map.Entry<String, Object> entry : resultJson.entrySet()) {
                        Integer dataId = Integer.parseInt(entry.getKey());
                        String result = entry.getValue().toString();
                        if ("是".equals(result)) {
                            digitalIds.add(dataId);
                        } else {
                            nonDigitalIds.add(dataId);
                        }
                    }
                    if (!digitalIds.isEmpty()) {
                        int digitalCount = batchUpdateStatus(digitalIds, "DIGITAL");
                        log.info("批量更新数字化项目状态成功，数量: {}", digitalCount);
                        processedCount += digitalCount;
                    }
                    if (!nonDigitalIds.isEmpty()) {
                        int nonDigitalCount = batchUpdateStatus(nonDigitalIds, "NON_DIGITAL");
                        log.info("批量更新非数字化项目状态成功，数量: {}", nonDigitalCount);
                        processedCount += nonDigitalCount;
                    }
                } catch (Exception e) {
                    log.error("处理批次数据失败", e);
                }
                array.clear();
            }
        }
        log.info("全部数据处理完成，共处理 {} 条记录", processedCount);
        return processedCount;
    }

    /**
     * 从API响应中提取JSON内容
     *
     * @param response API响应文本
     * @return 提取的JSON字符串
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.isEmpty()) {
            return "{}";
        }
        // 尝试匹配```json开头和```结尾的内容
        Pattern pattern = Pattern.compile("```(?:json)?\\s*(\\{[\\s\\S]*?\\})\\s*```");
        Matcher matcher = pattern.matcher(response);
        if (matcher.find()) {
            // 返回第一个捕获组（JSON内容）
            return matcher.group(1);
        }
        // 如果没有找到使用```包围的JSON，尝试直接匹配整个JSON对象
        pattern = Pattern.compile("\\{\\s*\"\\d+\"\\s*:\\s*\"[是否]\"[\\s\\S]*?\\}");
        matcher = pattern.matcher(response);
        if (matcher.find()) {
            return matcher.group(0);
        }
        // 如果都没找到，返回原始响应（去除可能存在的```json和```标记）
        return response.replaceAll("```json|```", "").trim();
    }
}

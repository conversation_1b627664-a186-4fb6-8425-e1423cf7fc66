package com.pitaya.devdiscovery.dstagbiaoxun.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 标讯检查Excel数据映射
 */
@Data
public class BiaoxunExcelDTO {

    @ExcelProperty("自然客户ID")
    private String naturalCustomerId;

    @ExcelProperty("自然客户名称")
    private String naturalCustomerName;

    @ExcelProperty("标讯标题")
    private String biddingTitle;

    @ExcelProperty("标讯内容")
    private String biddingContent;

    @ExcelProperty("发布时间")
    private String releaseTimeStr;

    @ExcelProperty("中标人")
    private String bidWinner;

    @ExcelProperty("r7")
    private String quantityStr;

    @ExcelProperty("地市")
    private String city;

    @ExcelProperty("项目编号")
    private String projectNumber;

    @ExcelProperty("项目名称")
    private String projectName;
}

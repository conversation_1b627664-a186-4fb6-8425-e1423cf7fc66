package com.pitaya.devdiscovery.dstagbiaoxun.controller;

import com.pitaya.devdiscovery.aiability.utils.InternalDeepSeekUtils;
import com.pitaya.devdiscovery.common.dto.ProcessResultDTO;
import com.pitaya.devdiscovery.common.response.BaseResponse;
import com.pitaya.devdiscovery.dstagbiaoxun.entity.TsanquanDsBiaoxuncheck;
import com.pitaya.devdiscovery.dstagbiaoxun.service.TsanquanDsBiaoxuncheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标讯检查控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/dstag/biaoxun")
public class DsTagBiaoxunController {
    @Autowired
    private InternalDeepSeekUtils internalDeepSeekUtils;

    @Autowired
    private TsanquanDsBiaoxuncheckService tsanquanDsBiaoxuncheckService;

    /**
     * 根据客户ID查询标讯检查信息
     *
     * @param naturalCustomerId 客户ID
     * @return 标讯检查信息
     */
    @GetMapping("/info/{naturalCustomerId}")
    public BaseResponse<TsanquanDsBiaoxuncheck> getBiaoxunInfo(@PathVariable String naturalCustomerId) {
        log.info("查询客户ID={}的标讯检查信息", naturalCustomerId);
        TsanquanDsBiaoxuncheck result = tsanquanDsBiaoxuncheckService.getById(naturalCustomerId);
        return BaseResponse.success(result);
    }

    /**
     * 根据状态查询标讯检查列表
     *
     * @param status 状态
     * @return 标讯检查信息列表
     */
    @GetMapping("/list")
    public BaseResponse<List<TsanquanDsBiaoxuncheck>> getBiaoxunList(@RequestParam String status) {
        log.info("查询状态为{}的标讯检查列表", status);
        List<TsanquanDsBiaoxuncheck> list = tsanquanDsBiaoxuncheckService.getByStatus(status);
        return BaseResponse.success(list);
    }

    /**
     * 更新标讯检查状态
     *
     * @param naturalCustomerId 客户ID
     * @param status            新状态
     * @return 处理结果
     */
    @PutMapping("/status/{naturalCustomerId}")
    public BaseResponse<ProcessResultDTO> updateStatus(
            @PathVariable String naturalCustomerId,
            @RequestParam String status) {
        log.info("更新客户ID={}的标讯检查状态为{}", naturalCustomerId, status);
        int count = tsanquanDsBiaoxuncheckService.updateStatusById(naturalCustomerId, status);
        return BaseResponse.success(ProcessResultDTO.of(count, "更新标讯检查状态成功"));
    }

    /**
     * 导入Excel数据
     *
     * @return 处理结果
     */
    @PostMapping("/import")
    public BaseResponse<ProcessResultDTO> importExcelData() {
        log.info("开始导入Excel数据");
        new Thread(()->{
            tsanquanDsBiaoxuncheckService.importFromExcel();
        }).start();
        return BaseResponse.success(ProcessResultDTO.of(0, "导入任务提交成功"));
    }

    /**
     * 处理标讯数据，调用DeepSeek API判断是否为数字化项目
     *
     * @return 处理结果
     */
    @PostMapping("/process")
    public BaseResponse<ProcessResultDTO> processData() {
        log.info("开始处理标讯数据，调用DeepSeek API判断是否为数字化项目");
        int count = tsanquanDsBiaoxuncheckService.processData();
        return BaseResponse.success(ProcessResultDTO.of(count, "成功处理" + count + "条数据"));
    }
}

package com.pitaya.devdiscovery.dstagbiaoxun.schedule;

import com.pitaya.devdiscovery.common.dto.ProcessResultDTO;
import com.pitaya.devdiscovery.common.response.BaseResponse;
import com.pitaya.devdiscovery.dstagbiaoxun.service.TsanquanDsBiaoxuncheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @program: dev-discovery
 * @ClassName DsTagBiaoxunSchedule
 * @description:
 * @author: ma<PERSON>an
 * @date: 2025-05-12 18:12
 * @Version 1.0
 **/
@Slf4j
@Component
public class DsTagBiaoxunSchedule {
    @Autowired
    private TsanquanDsBiaoxuncheckService tsanquanDsBiaoxuncheckService;

    //@Scheduled(cron = "0/5 * * * * ?")
    public void processCustomerMatchTasksSchedule() {
        log.info("开始处理标讯数据，调用DeepSeek API判断是否为数字化项目");
        int count = tsanquanDsBiaoxuncheckService.processData();
        log.info("成功处理{}条数据", count);
    }
}
